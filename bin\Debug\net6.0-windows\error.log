﻿[2025-10-02 03:05:41Z] === Application Starting ===
[2025-10-02 03:05:41Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-02 03:05:41Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-02 03:05:41Z] Base Directory: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 03:05:41Z] UI Culture set to Arabic (Egypt)
[2025-10-02 03:05:41Z] === System Requirements Check ===
[2025-10-02 03:05:41Z] Available memory: 0 MB
[2025-10-02 03:05:41Z] Warning: Low memory detected. Application may run slowly.
[2025-10-02 03:05:45Z] Running in 64-bit process: True
[2025-10-02 03:05:45Z] 64-bit Operating System: True
[2025-10-02 03:05:45Z] Windows Forms support verified
[2025-10-02 03:05:45Z] === File System Check ===
[2025-10-02 03:05:45Z] Application directory: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 03:05:45Z] Looking for questions file at: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-02 03:05:45Z] Questions file found at expected location
[2025-10-02 03:05:45Z] Write permissions verified
[2025-10-02 03:05:45Z] === Application Initialization ===
[2025-10-02 03:05:45Z] Initializing GameManager...
[2025-10-02 03:05:45Z] Creating StartForm...
[2025-10-02 03:05:45Z] Starting application main loop...
[2025-10-02 03:08:58Z] Application main loop ended normally.
[2025-10-02 03:08:58Z] === Application Ending ===
[2025-10-02 03:08:58Z] End time: 02/10/2025 03:08:58 ص
[2025-10-02 03:29:08Z] === Application Starting ===
[2025-10-02 03:29:08Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-02 03:29:08Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-02 03:29:08Z] Base Directory: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 03:29:08Z] UI Culture set to English (US)
[2025-10-02 03:29:08Z] === System Requirements Check ===
[2025-10-02 03:29:08Z] Available memory: 0 MB
[2025-10-02 03:29:08Z] Warning: Low memory detected. Application may run slowly.
[2025-10-02 03:29:10Z] Running in 64-bit process: True
[2025-10-02 03:29:10Z] 64-bit Operating System: True
[2025-10-02 03:29:11Z] Windows Forms support verified
[2025-10-02 03:29:11Z] === File System Check ===
[2025-10-02 03:29:11Z] Application directory: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 03:29:11Z] Looking for questions file at: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-02 03:29:11Z] Questions file found at expected location
[2025-10-02 03:29:11Z] Write permissions verified
[2025-10-02 03:29:11Z] === Application Initialization ===
[2025-10-02 03:29:11Z] Initializing GameManager...
[2025-10-02 03:29:11Z] Creating StartForm...
[2025-10-02 03:29:11Z] Starting application main loop...
[2025-10-02 03:29:25Z] Application main loop ended normally.
[2025-10-02 03:29:25Z] === Application Ending ===
[2025-10-02 03:29:25Z] End time: 02/10/2025 03:29:25 ص
[2025-10-02 03:33:01Z] === Application Starting ===
[2025-10-02 03:33:01Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-02 03:33:01Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-02 03:33:01Z] Base Directory: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 03:33:01Z] UI Culture set to English (US)
[2025-10-02 03:33:01Z] === System Requirements Check ===
[2025-10-02 03:33:01Z] Available memory: 0 MB
[2025-10-02 03:33:01Z] Warning: Low memory detected. Application may run slowly.
[2025-10-02 03:33:04Z] Running in 64-bit process: True
[2025-10-02 03:33:04Z] 64-bit Operating System: True
[2025-10-02 03:33:04Z] Windows Forms support verified
[2025-10-02 03:33:04Z] === File System Check ===
[2025-10-02 03:33:04Z] Application directory: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 03:33:04Z] Looking for questions file at: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-02 03:33:04Z] Questions file found at expected location
[2025-10-02 03:33:04Z] Write permissions verified
[2025-10-02 03:33:04Z] === Application Initialization ===
[2025-10-02 03:33:04Z] Initializing GameManager...
[2025-10-02 03:33:04Z] Creating StartForm...
[2025-10-02 03:33:04Z] Starting application main loop...
[2025-10-02 03:33:13Z] Application main loop ended normally.
[2025-10-02 03:33:13Z] === Application Ending ===
[2025-10-02 03:33:13Z] End time: 02/10/2025 03:33:13 ص
[2025-10-02 16:27:33Z] === Application Starting ===
[2025-10-02 16:27:33Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-02 16:27:33Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-02 16:27:33Z] Base Directory: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 16:27:33Z] UI Culture set to English (US)
[2025-10-02 16:27:33Z] === System Requirements Check ===
[2025-10-02 16:27:33Z] Available memory: 0 MB
[2025-10-02 16:27:33Z] Warning: Low memory detected. Application may run slowly.
[2025-10-02 16:27:36Z] Running in 64-bit process: True
[2025-10-02 16:27:36Z] 64-bit Operating System: True
[2025-10-02 16:27:36Z] Windows Forms support verified
[2025-10-02 16:27:36Z] === File System Check ===
[2025-10-02 16:27:36Z] Application directory: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 16:27:36Z] Looking for questions file at: c:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-02 16:27:36Z] Questions file found at expected location
[2025-10-02 16:27:36Z] Write permissions verified
[2025-10-02 16:27:36Z] === Application Initialization ===
[2025-10-02 16:27:36Z] Initializing GameManager...
[2025-10-02 16:27:36Z] Creating StartForm...
[2025-10-02 16:27:36Z] Starting application main loop...
[2025-10-02 16:29:12Z] Application main loop ended normally.
[2025-10-02 16:29:12Z] === Application Ending ===
[2025-10-02 16:29:12Z] End time: 02/10/2025 04:29:12 م
[2025-10-02 17:17:59Z] === Application Starting ===
[2025-10-02 17:17:59Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-02 17:17:59Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-02 17:17:59Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 17:17:59Z] UI Culture set to English (US)
[2025-10-02 17:17:59Z] === System Requirements Check ===
[2025-10-02 17:17:59Z] Available memory: 0 MB
[2025-10-02 17:17:59Z] Warning: Low memory detected. Application may run slowly.
[2025-10-02 17:18:02Z] Running in 64-bit process: True
[2025-10-02 17:18:02Z] 64-bit Operating System: True
[2025-10-02 17:18:02Z] Windows Forms support verified
[2025-10-02 17:18:02Z] === File System Check ===
[2025-10-02 17:18:02Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-02 17:18:02Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-02 17:18:02Z] Questions file found at expected location
[2025-10-02 17:18:02Z] Write permissions verified
[2025-10-02 17:18:02Z] === Application Initialization ===
[2025-10-02 17:18:02Z] Initializing GameManager...
[2025-10-02 17:18:02Z] Creating StartForm...
[2025-10-02 17:18:02Z] Starting application main loop...
[2025-10-02 17:18:42Z] Application main loop ended normally.
[2025-10-02 17:18:42Z] === Application Ending ===
[2025-10-02 17:18:42Z] End time: 02/10/2025 05:18:42 م
[2025-10-03 10:52:08Z] === Application Starting ===
[2025-10-03 10:52:08Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 10:52:08Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 10:52:08Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 10:52:08Z] UI Culture set to English (US)
[2025-10-03 10:52:08Z] === System Requirements Check ===
[2025-10-03 10:52:08Z] Available memory: 0 MB
[2025-10-03 10:52:08Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 10:52:14Z] Running in 64-bit process: True
[2025-10-03 10:52:14Z] 64-bit Operating System: True
[2025-10-03 10:52:14Z] Windows Forms support verified
[2025-10-03 10:52:14Z] === File System Check ===
[2025-10-03 10:52:14Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 10:52:14Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 10:52:14Z] Questions file found at expected location
[2025-10-03 10:52:14Z] Write permissions verified
[2025-10-03 10:52:14Z] === Application Initialization ===
[2025-10-03 10:52:14Z] Initializing GameManager...
[2025-10-03 10:52:14Z] Creating StartForm...
[2025-10-03 10:52:14Z] Starting application main loop...
[2025-10-03 10:53:06Z] Saving game result for player: Player_10031053, Amount: 0
[2025-10-03 10:53:06Z] Results file does not exist, returning empty list
[2025-10-03 10:53:06Z] Game result saved successfully
[2025-10-03 10:53:09Z] Application main loop ended normally.
[2025-10-03 10:53:09Z] === Application Ending ===
[2025-10-03 10:53:09Z] End time: 03/10/2025 10:53:09 ص
[2025-10-03 11:24:24Z] === Application Starting ===
[2025-10-03 11:24:24Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 11:24:24Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 11:24:24Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:24:24Z] UI Culture set to English (US)
[2025-10-03 11:24:24Z] === System Requirements Check ===
[2025-10-03 11:24:24Z] Available memory: 0 MB
[2025-10-03 11:24:24Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 11:24:27Z] Running in 64-bit process: True
[2025-10-03 11:24:27Z] 64-bit Operating System: True
[2025-10-03 11:24:27Z] Windows Forms support verified
[2025-10-03 11:24:27Z] === File System Check ===
[2025-10-03 11:24:27Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:24:27Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:24:27Z] Questions file found at expected location
[2025-10-03 11:24:27Z] Write permissions verified
[2025-10-03 11:24:27Z] === Application Initialization ===
[2025-10-03 11:24:27Z] Initializing GameManager...
[2025-10-03 11:24:27Z] Creating MainMenuForm...
[2025-10-03 11:24:28Z] Sound system initialized successfully
[2025-10-03 11:24:28Z] Starting application main loop...
[2025-10-03 11:24:32Z] Starting new game from main menu
[2025-10-03 11:24:32Z] No questions available for level 1
[2025-10-03 11:24:56Z] Application main loop ended normally.
[2025-10-03 11:24:56Z] === Application Ending ===
[2025-10-03 11:24:56Z] End time: 03/10/2025 11:24:56 ص
[2025-10-03 11:31:58Z] === Application Starting ===
[2025-10-03 11:31:58Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 11:31:58Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 11:31:58Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:31:58Z] UI Culture set to English (US)
[2025-10-03 11:31:58Z] === System Requirements Check ===
[2025-10-03 11:31:58Z] Available memory: 0 MB
[2025-10-03 11:31:58Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 11:32:01Z] Running in 64-bit process: True
[2025-10-03 11:32:01Z] 64-bit Operating System: True
[2025-10-03 11:32:01Z] Windows Forms support verified
[2025-10-03 11:32:01Z] === File System Check ===
[2025-10-03 11:32:01Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:32:01Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:32:01Z] Questions file found at expected location
[2025-10-03 11:32:01Z] Write permissions verified
[2025-10-03 11:32:01Z] === Application Initialization ===
[2025-10-03 11:32:01Z] Initializing GameManager...
[2025-10-03 11:32:01Z] Creating MainMenuForm...
[2025-10-03 11:32:01Z] Sound system initialized successfully
[2025-10-03 11:32:01Z] Starting application main loop...
[2025-10-03 11:32:03Z] Starting new game from main menu
[2025-10-03 11:32:03Z] No questions available for level 1
[2025-10-03 11:32:58Z] Application main loop ended normally.
[2025-10-03 11:32:58Z] === Application Ending ===
[2025-10-03 11:32:58Z] End time: 03/10/2025 11:32:58 ص
[2025-10-03 11:39:15Z] === Application Starting ===
[2025-10-03 11:39:15Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 11:39:15Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 11:39:15Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:39:15Z] UI Culture set to English (US)
[2025-10-03 11:39:15Z] === System Requirements Check ===
[2025-10-03 11:39:15Z] Available memory: 0 MB
[2025-10-03 11:39:15Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 11:39:18Z] Running in 64-bit process: True
[2025-10-03 11:39:18Z] 64-bit Operating System: True
[2025-10-03 11:39:18Z] Windows Forms support verified
[2025-10-03 11:39:18Z] === File System Check ===
[2025-10-03 11:39:18Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:39:18Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:39:18Z] Questions file found at expected location
[2025-10-03 11:39:18Z] Write permissions verified
[2025-10-03 11:39:18Z] === Application Initialization ===
[2025-10-03 11:39:18Z] Initializing GameManager...
[2025-10-03 11:39:18Z] Creating MainMenuForm...
[2025-10-03 11:39:18Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:39:18Z] Starting application main loop...
[2025-10-03 11:39:27Z] Starting new game from main menu
[2025-10-03 11:39:27Z] Using default game settings
[2025-10-03 11:39:27Z] ----- Exception -----
Message: Cannot call this method when SelectionMode is SelectionMode.NONE. (Parameter 'value')
Type: System.ArgumentException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.ListBox.set_SelectedIndex(Int32 value)
   at MillionaireGame.QuestionForm.LoadNewQuestion() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\QuestionForm.cs:line 342
   at MillionaireGame.QuestionForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\QuestionForm.cs:line 45
   at MillionaireGame.MainMenuForm.BtnNewGame_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\MainMenuForm.cs:line 212
   at System.Windows.Forms.Control.OnClick(EventArgs e)
   at System.Windows.Forms.Button.OnClick(EventArgs e)
   at System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   at System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   at System.Windows.Forms.Control.WndProc(Message& m)
   at System.Windows.Forms.ButtonBase.WndProc(Message& m)
   at System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   at System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, WM msg, IntPtr wparam, IntPtr lparam)
---------------------

[2025-10-03 11:39:30Z] Starting new game from main menu
[2025-10-03 11:39:30Z] ----- Exception -----
Message: Cannot call this method when SelectionMode is SelectionMode.NONE. (Parameter 'value')
Type: System.ArgumentException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.ListBox.set_SelectedIndex(Int32 value)
   at MillionaireGame.QuestionForm.LoadNewQuestion() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\QuestionForm.cs:line 342
   at MillionaireGame.QuestionForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\QuestionForm.cs:line 45
   at MillionaireGame.MainMenuForm.BtnNewGame_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\MainMenuForm.cs:line 212
   at System.Windows.Forms.Control.OnClick(EventArgs e)
   at System.Windows.Forms.Button.OnClick(EventArgs e)
   at System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   at System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   at System.Windows.Forms.Control.WndProc(Message& m)
   at System.Windows.Forms.ButtonBase.WndProc(Message& m)
   at System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   at System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, WM msg, IntPtr wparam, IntPtr lparam)
---------------------

[2025-10-03 11:39:43Z] Starting new game from main menu
[2025-10-03 11:39:43Z] ----- Exception -----
Message: Cannot call this method when SelectionMode is SelectionMode.NONE. (Parameter 'value')
Type: System.ArgumentException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.ListBox.set_SelectedIndex(Int32 value)
   at MillionaireGame.QuestionForm.LoadNewQuestion() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\QuestionForm.cs:line 342
   at MillionaireGame.QuestionForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\QuestionForm.cs:line 45
   at MillionaireGame.MainMenuForm.BtnNewGame_Click(Object sender, EventArgs e) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\MainMenuForm.cs:line 212
   at System.Windows.Forms.Control.OnClick(EventArgs e)
   at System.Windows.Forms.Button.OnClick(EventArgs e)
   at System.Windows.Forms.Button.OnMouseUp(MouseEventArgs mevent)
   at System.Windows.Forms.Control.WmMouseUp(Message& m, MouseButtons button, Int32 clicks)
   at System.Windows.Forms.Control.WndProc(Message& m)
   at System.Windows.Forms.ButtonBase.WndProc(Message& m)
   at System.Windows.Forms.Control.ControlNativeWindow.WndProc(Message& m)
   at System.Windows.Forms.NativeWindow.Callback(IntPtr hWnd, WM msg, IntPtr wparam, IntPtr lparam)
---------------------

[2025-10-03 11:40:04Z] Application main loop ended normally.
[2025-10-03 11:40:04Z] === Application Ending ===
[2025-10-03 11:40:04Z] End time: 03/10/2025 11:40:04 ص
[2025-10-03 11:41:18Z] === Application Starting ===
[2025-10-03 11:41:18Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 11:41:18Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 11:41:18Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:41:18Z] UI Culture set to English (US)
[2025-10-03 11:41:18Z] === System Requirements Check ===
[2025-10-03 11:41:18Z] Available memory: 0 MB
[2025-10-03 11:41:18Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 11:41:26Z] Running in 64-bit process: True
[2025-10-03 11:41:26Z] 64-bit Operating System: True
[2025-10-03 11:41:26Z] Windows Forms support verified
[2025-10-03 11:41:26Z] === File System Check ===
[2025-10-03 11:41:26Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:41:26Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:41:26Z] Questions file found at expected location
[2025-10-03 11:41:26Z] Write permissions verified
[2025-10-03 11:41:26Z] === Application Initialization ===
[2025-10-03 11:41:26Z] Initializing GameManager...
[2025-10-03 11:41:26Z] Creating MainMenuForm...
[2025-10-03 11:41:26Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:41:26Z] Starting application main loop...
[2025-10-03 11:41:27Z] Starting new game from main menu
[2025-10-03 11:41:27Z] Using default game settings
[2025-10-03 11:41:49Z] Application main loop ended normally.
[2025-10-03 11:41:49Z] === Application Ending ===
[2025-10-03 11:41:49Z] End time: 03/10/2025 11:41:49 ص
[2025-10-03 11:45:25Z] === Application Starting ===
[2025-10-03 11:45:25Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 11:45:25Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 11:45:25Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:45:25Z] UI Culture set to English (US)
[2025-10-03 11:45:25Z] === System Requirements Check ===
[2025-10-03 11:45:25Z] Available memory: 0 MB
[2025-10-03 11:45:25Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 11:45:32Z] Running in 64-bit process: True
[2025-10-03 11:45:32Z] 64-bit Operating System: True
[2025-10-03 11:45:32Z] Windows Forms support verified
[2025-10-03 11:45:32Z] === File System Check ===
[2025-10-03 11:45:32Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:45:32Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:45:32Z] Questions file found at expected location
[2025-10-03 11:45:32Z] Write permissions verified
[2025-10-03 11:45:32Z] === Application Initialization ===
[2025-10-03 11:45:32Z] Initializing GameManager...
[2025-10-03 11:45:32Z] Creating MainMenuForm...
[2025-10-03 11:45:32Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:45:32Z] Starting application main loop...
[2025-10-03 11:45:35Z] Starting new game from main menu
[2025-10-03 11:45:35Z] Using default game settings
[2025-10-03 11:46:03Z] Application main loop ended normally.
[2025-10-03 11:46:03Z] === Application Ending ===
[2025-10-03 11:46:03Z] End time: 03/10/2025 11:46:03 ص
[2025-10-03 11:53:25Z] === Application Starting ===
[2025-10-03 11:53:25Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 11:53:25Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 11:53:25Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:53:25Z] UI Culture set to English (US)
[2025-10-03 11:53:25Z] === System Requirements Check ===
[2025-10-03 11:53:25Z] Available memory: 0 MB
[2025-10-03 11:53:25Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 11:53:28Z] Running in 64-bit process: True
[2025-10-03 11:53:28Z] 64-bit Operating System: True
[2025-10-03 11:53:28Z] Windows Forms support verified
[2025-10-03 11:53:28Z] === File System Check ===
[2025-10-03 11:53:28Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:53:28Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:53:28Z] Questions file found at expected location
[2025-10-03 11:53:28Z] Write permissions verified
[2025-10-03 11:53:28Z] === Application Initialization ===
[2025-10-03 11:53:28Z] Creating MainMenuForm...
[2025-10-03 11:53:28Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:53:28Z] Starting application main loop...
[2025-10-03 11:53:30Z] Starting new game from main menu
[2025-10-03 11:53:30Z] Using default game settings
[2025-10-03 11:53:41Z] Application main loop ended normally.
[2025-10-03 11:53:41Z] === Application Ending ===
[2025-10-03 11:53:41Z] End time: 03/10/2025 11:53:41 ص
[2025-10-03 11:56:30Z] === Application Starting ===
[2025-10-03 11:56:30Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 11:56:30Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 11:56:30Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:56:30Z] UI Culture set to English (US)
[2025-10-03 11:56:30Z] === System Requirements Check ===
[2025-10-03 11:56:30Z] Available memory: 0 MB
[2025-10-03 11:56:30Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 11:56:33Z] Running in 64-bit process: True
[2025-10-03 11:56:33Z] 64-bit Operating System: True
[2025-10-03 11:56:33Z] Windows Forms support verified
[2025-10-03 11:56:33Z] === File System Check ===
[2025-10-03 11:56:33Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 11:56:33Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:56:33Z] Questions file found at expected location
[2025-10-03 11:56:33Z] Write permissions verified
[2025-10-03 11:56:33Z] === Application Initialization ===
[2025-10-03 11:56:33Z] Creating MainMenuForm...
[2025-10-03 11:56:33Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 11:56:34Z] Starting application main loop...
[2025-10-03 11:56:35Z] Starting new game from main menu
[2025-10-03 11:56:35Z] Using default game settings
[2025-10-03 12:11:34Z] === Application Starting ===
[2025-10-03 12:11:34Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 12:11:34Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 12:11:34Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 12:11:34Z] UI Culture set to English (US)
[2025-10-03 12:11:34Z] === System Requirements Check ===
[2025-10-03 12:11:34Z] Available memory: 0 MB
[2025-10-03 12:11:34Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 12:11:38Z] Running in 64-bit process: True
[2025-10-03 12:11:38Z] 64-bit Operating System: True
[2025-10-03 12:11:38Z] Windows Forms support verified
[2025-10-03 12:11:38Z] === File System Check ===
[2025-10-03 12:11:38Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 12:11:38Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 12:11:38Z] Questions file found at expected location
[2025-10-03 12:11:38Z] Write permissions verified
[2025-10-03 12:11:38Z] === Application Initialization ===
[2025-10-03 12:11:38Z] Creating MainMenuForm...
[2025-10-03 12:11:38Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 12:11:38Z] Starting application main loop...
[2025-10-03 12:11:39Z] Starting new game from main menu
[2025-10-03 12:11:39Z] Using default game settings
[2025-10-03 12:11:56Z] Application main loop ended normally.
[2025-10-03 12:11:56Z] === Application Ending ===
[2025-10-03 12:11:56Z] End time: 03/10/2025 12:11:56 م
[2025-10-03 15:12:06Z] === Application Starting ===
[2025-10-03 15:12:06Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 15:12:06Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 15:12:06Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 15:12:06Z] UI Culture set to English (US)
[2025-10-03 15:12:06Z] === System Requirements Check ===
[2025-10-03 15:12:06Z] Available memory: 0 MB
[2025-10-03 15:12:06Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 15:12:08Z] Running in 64-bit process: True
[2025-10-03 15:12:08Z] 64-bit Operating System: True
[2025-10-03 15:12:08Z] Windows Forms support verified
[2025-10-03 15:12:08Z] === File System Check ===
[2025-10-03 15:12:08Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 15:12:08Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 15:12:08Z] Questions file found at expected location
[2025-10-03 15:12:08Z] Write permissions verified
[2025-10-03 15:12:08Z] === Application Initialization ===
[2025-10-03 15:12:08Z] Creating MainMenuForm...
[2025-10-03 15:12:08Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 15:12:08Z] Starting application main loop...
[2025-10-03 15:12:10Z] Starting new game from main menu
[2025-10-03 15:12:10Z] Using default game settings
[2025-10-03 15:13:25Z] Application main loop ended normally.
[2025-10-03 15:13:25Z] === Application Ending ===
[2025-10-03 15:13:25Z] End time: 03/10/2025 03:13:25 م
[2025-10-03 15:25:22Z] === Application Starting ===
[2025-10-03 15:25:22Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 15:25:22Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 15:25:22Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 15:25:22Z] UI Culture set to English (US)
[2025-10-03 15:25:22Z] === System Requirements Check ===
[2025-10-03 15:25:22Z] Available memory: 0 MB
[2025-10-03 15:25:22Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 15:25:25Z] Running in 64-bit process: True
[2025-10-03 15:25:25Z] 64-bit Operating System: True
[2025-10-03 15:25:25Z] Windows Forms support verified
[2025-10-03 15:25:25Z] === File System Check ===
[2025-10-03 15:25:25Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 15:25:25Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 15:25:25Z] Questions file found at expected location
[2025-10-03 15:25:25Z] Write permissions verified
[2025-10-03 15:25:25Z] === Application Initialization ===
[2025-10-03 15:25:25Z] Creating MainMenuForm...
[2025-10-03 15:25:25Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 15:25:25Z] Starting application main loop...
[2025-10-03 15:25:28Z] Starting new game from main menu
[2025-10-03 15:25:28Z] Using default game settings
[2025-10-03 15:26:06Z] Application main loop ended normally.
[2025-10-03 15:26:06Z] === Application Ending ===
[2025-10-03 15:26:06Z] End time: 03/10/2025 03:26:06 م
[2025-10-03 15:53:12Z] === Application Starting ===
[2025-10-03 15:53:12Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 15:53:12Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 15:53:12Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 15:53:12Z] UI Culture set to English (US)
[2025-10-03 15:53:12Z] === System Requirements Check ===
[2025-10-03 15:53:12Z] Available memory: 0 MB
[2025-10-03 15:53:12Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 15:53:15Z] Running in 64-bit process: True
[2025-10-03 15:53:15Z] 64-bit Operating System: True
[2025-10-03 15:53:15Z] Windows Forms support verified
[2025-10-03 15:53:15Z] === File System Check ===
[2025-10-03 15:53:15Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 15:53:15Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 15:53:15Z] Questions file found at expected location
[2025-10-03 15:53:15Z] Write permissions verified
[2025-10-03 15:53:15Z] === Application Initialization ===
[2025-10-03 15:53:15Z] Creating MainMenuForm...
[2025-10-03 15:53:15Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 15:53:15Z] Starting application main loop...
[2025-10-03 15:53:54Z] Using default game settings
[2025-10-03 15:54:00Z] Loaded 1 game results
[2025-10-03 15:54:04Z] Loaded 1 game results
[2025-10-03 15:54:08Z] Starting new game from main menu
[2025-10-03 15:54:27Z] Application main loop ended normally.
[2025-10-03 15:54:27Z] === Application Ending ===
[2025-10-03 15:54:27Z] End time: 03/10/2025 03:54:27 م
[2025-10-03 16:00:23Z] === Application Starting ===
[2025-10-03 16:00:23Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:00:23Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:00:23Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:00:23Z] UI Culture set to English (US)
[2025-10-03 16:00:23Z] === System Requirements Check ===
[2025-10-03 16:00:23Z] Available memory: 0 MB
[2025-10-03 16:00:23Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:00:25Z] Running in 64-bit process: True
[2025-10-03 16:00:25Z] 64-bit Operating System: True
[2025-10-03 16:00:25Z] Windows Forms support verified
[2025-10-03 16:00:25Z] === File System Check ===
[2025-10-03 16:00:25Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:00:25Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:00:25Z] Questions file found at expected location
[2025-10-03 16:00:25Z] Write permissions verified
[2025-10-03 16:00:25Z] === Application Initialization ===
[2025-10-03 16:00:25Z] Creating MainMenuForm...
[2025-10-03 16:00:25Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:00:25Z] Starting application main loop...
[2025-10-03 16:00:33Z] Starting new game from main menu
[2025-10-03 16:00:33Z] Using default game settings
[2025-10-03 16:01:05Z] Application main loop ended normally.
[2025-10-03 16:01:05Z] === Application Ending ===
[2025-10-03 16:01:05Z] End time: 03/10/2025 04:01:05 م
[2025-10-03 16:04:29Z] === Application Starting ===
[2025-10-03 16:04:29Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:04:29Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:04:29Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:04:29Z] UI Culture set to English (US)
[2025-10-03 16:04:29Z] === System Requirements Check ===
[2025-10-03 16:04:29Z] Available memory: 0 MB
[2025-10-03 16:04:29Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:04:31Z] Running in 64-bit process: True
[2025-10-03 16:04:31Z] 64-bit Operating System: True
[2025-10-03 16:04:31Z] Windows Forms support verified
[2025-10-03 16:04:31Z] === File System Check ===
[2025-10-03 16:04:31Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:04:31Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:04:31Z] Questions file found at expected location
[2025-10-03 16:04:31Z] Write permissions verified
[2025-10-03 16:04:31Z] === Application Initialization ===
[2025-10-03 16:04:31Z] Creating MainMenuForm...
[2025-10-03 16:04:31Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:04:31Z] Starting application main loop...
[2025-10-03 16:04:45Z] Loaded 1 game results
[2025-10-03 16:06:26Z] === Application Starting ===
[2025-10-03 16:06:26Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:06:26Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:06:26Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:06:26Z] UI Culture set to English (US)
[2025-10-03 16:06:26Z] === System Requirements Check ===
[2025-10-03 16:06:26Z] Available memory: 0 MB
[2025-10-03 16:06:26Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:06:28Z] Running in 64-bit process: True
[2025-10-03 16:06:28Z] 64-bit Operating System: True
[2025-10-03 16:06:28Z] Windows Forms support verified
[2025-10-03 16:06:28Z] === File System Check ===
[2025-10-03 16:06:28Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:06:28Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:06:28Z] Questions file found at expected location
[2025-10-03 16:06:28Z] Write permissions verified
[2025-10-03 16:06:28Z] === Application Initialization ===
[2025-10-03 16:06:28Z] Creating MainMenuForm...
[2025-10-03 16:06:28Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:06:28Z] Starting application main loop...
[2025-10-03 16:18:57Z] === Application Starting ===
[2025-10-03 16:18:57Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:18:57Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:18:57Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:18:57Z] UI Culture set to English (US)
[2025-10-03 16:18:57Z] === System Requirements Check ===
[2025-10-03 16:18:57Z] Available memory: 0 MB
[2025-10-03 16:18:57Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:19:00Z] Running in 64-bit process: True
[2025-10-03 16:19:00Z] 64-bit Operating System: True
[2025-10-03 16:19:00Z] Windows Forms support verified
[2025-10-03 16:19:00Z] === File System Check ===
[2025-10-03 16:19:00Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:19:00Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:19:00Z] Questions file found at expected location
[2025-10-03 16:19:00Z] Write permissions verified
[2025-10-03 16:19:00Z] === Application Initialization ===
[2025-10-03 16:19:00Z] Creating MainMenuForm...
[2025-10-03 16:19:00Z] Questions loaded successfully from: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:19:00Z] Starting application main loop...
[2025-10-03 16:19:37Z] Application main loop ended normally.
[2025-10-03 16:19:37Z] === Application Ending ===
[2025-10-03 16:19:37Z] End time: 03/10/2025 04:19:37 م
[2025-10-03 16:20:51Z] === Application Starting ===
[2025-10-03 16:20:51Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:20:51Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:20:51Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:20:51Z] UI Culture set to English (US)
[2025-10-03 16:20:51Z] === System Requirements Check ===
[2025-10-03 16:20:51Z] Available memory: 0 MB
[2025-10-03 16:20:51Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:20:56Z] Running in 64-bit process: True
[2025-10-03 16:20:56Z] 64-bit Operating System: True
[2025-10-03 16:20:56Z] Windows Forms support verified
[2025-10-03 16:20:56Z] === File System Check ===
[2025-10-03 16:20:56Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:20:56Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:20:56Z] Questions file found at expected location
[2025-10-03 16:20:56Z] Write permissions verified
[2025-10-03 16:20:56Z] === Application Initialization ===
[2025-10-03 16:20:56Z] Creating StartForm...
[2025-10-03 16:20:56Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 160
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 92
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 29
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 16:20:59Z] === Application Ending ===
[2025-10-03 16:20:59Z] End time: 03/10/2025 04:20:59 م
[2025-10-03 16:23:40Z] === Application Starting ===
[2025-10-03 16:23:40Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:23:40Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:23:40Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:23:40Z] UI Culture set to English (US)
[2025-10-03 16:23:40Z] === System Requirements Check ===
[2025-10-03 16:23:40Z] Available memory: 0 MB
[2025-10-03 16:23:40Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:23:43Z] Running in 64-bit process: True
[2025-10-03 16:23:43Z] 64-bit Operating System: True
[2025-10-03 16:23:43Z] Windows Forms support verified
[2025-10-03 16:23:43Z] === File System Check ===
[2025-10-03 16:23:43Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:23:43Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:23:43Z] Questions file found at expected location
[2025-10-03 16:23:43Z] Write permissions verified
[2025-10-03 16:23:43Z] === Application Initialization ===
[2025-10-03 16:23:43Z] Creating StartForm...
[2025-10-03 16:23:43Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 158
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 91
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 29
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 16:23:50Z] === Application Ending ===
[2025-10-03 16:23:50Z] End time: 03/10/2025 04:23:50 م
[2025-10-03 16:50:53Z] === Application Starting ===
[2025-10-03 16:50:53Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:50:53Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:50:53Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:50:53Z] UI Culture set to English (US)
[2025-10-03 16:50:53Z] === System Requirements Check ===
[2025-10-03 16:50:53Z] Available memory: 0 MB
[2025-10-03 16:50:53Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:50:56Z] Running in 64-bit process: True
[2025-10-03 16:50:56Z] 64-bit Operating System: True
[2025-10-03 16:50:56Z] Windows Forms support verified
[2025-10-03 16:50:56Z] === File System Check ===
[2025-10-03 16:50:56Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:50:56Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:50:56Z] Questions file found at expected location
[2025-10-03 16:50:56Z] Write permissions verified
[2025-10-03 16:50:56Z] === Application Initialization ===
[2025-10-03 16:50:56Z] Creating StartForm...
[2025-10-03 16:50:56Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 159
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 92
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 30
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 16:51:18Z] === Application Ending ===
[2025-10-03 16:51:18Z] End time: 03/10/2025 04:51:18 م
[2025-10-03 16:53:01Z] === Application Starting ===
[2025-10-03 16:53:01Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:53:01Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:53:01Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:53:01Z] UI Culture set to English (US)
[2025-10-03 16:53:01Z] === System Requirements Check ===
[2025-10-03 16:53:01Z] Available memory: 0 MB
[2025-10-03 16:53:01Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:53:04Z] Running in 64-bit process: True
[2025-10-03 16:53:04Z] 64-bit Operating System: True
[2025-10-03 16:53:04Z] Windows Forms support verified
[2025-10-03 16:53:04Z] === File System Check ===
[2025-10-03 16:53:04Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:53:04Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:53:04Z] Questions file found at expected location
[2025-10-03 16:53:04Z] Write permissions verified
[2025-10-03 16:53:04Z] === Application Initialization ===
[2025-10-03 16:53:04Z] Creating StartForm...
[2025-10-03 16:53:04Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 159
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 92
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 30
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 16:53:05Z] === Application Ending ===
[2025-10-03 16:53:05Z] End time: 03/10/2025 04:53:05 م
[2025-10-03 16:57:39Z] === Application Starting ===
[2025-10-03 16:57:39Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 16:57:39Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 16:57:39Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:57:39Z] UI Culture set to English (US)
[2025-10-03 16:57:39Z] === System Requirements Check ===
[2025-10-03 16:57:39Z] Available memory: 0 MB
[2025-10-03 16:57:39Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 16:57:44Z] Running in 64-bit process: True
[2025-10-03 16:57:44Z] 64-bit Operating System: True
[2025-10-03 16:57:44Z] Windows Forms support verified
[2025-10-03 16:57:44Z] === File System Check ===
[2025-10-03 16:57:44Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 16:57:44Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 16:57:44Z] Questions file found at expected location
[2025-10-03 16:57:44Z] Write permissions verified
[2025-10-03 16:57:44Z] === Application Initialization ===
[2025-10-03 16:57:44Z] Creating StartForm...
[2025-10-03 16:57:44Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 159
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 92
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 30
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 16:57:46Z] === Application Ending ===
[2025-10-03 16:57:46Z] End time: 03/10/2025 04:57:46 م
[2025-10-03 17:00:24Z] === Application Starting ===
[2025-10-03 17:00:24Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 17:00:24Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 17:00:24Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:00:24Z] UI Culture set to English (US)
[2025-10-03 17:00:24Z] === System Requirements Check ===
[2025-10-03 17:00:24Z] Available memory: 0 MB
[2025-10-03 17:00:24Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 17:00:33Z] Running in 64-bit process: True
[2025-10-03 17:00:33Z] 64-bit Operating System: True
[2025-10-03 17:00:33Z] Windows Forms support verified
[2025-10-03 17:00:33Z] === File System Check ===
[2025-10-03 17:00:33Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:00:33Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 17:00:33Z] Questions file found at expected location
[2025-10-03 17:00:33Z] Write permissions verified
[2025-10-03 17:00:33Z] === Application Initialization ===
[2025-10-03 17:00:33Z] Creating StartForm...
[2025-10-03 17:00:33Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 17:00:41Z] === Application Ending ===
[2025-10-03 17:00:41Z] End time: 03/10/2025 05:00:41 م
[2025-10-03 17:03:42Z] === Application Starting ===
[2025-10-03 17:03:42Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 17:03:42Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 17:03:42Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:03:42Z] UI Culture set to English (US)
[2025-10-03 17:03:42Z] === System Requirements Check ===
[2025-10-03 17:03:42Z] Available memory: 0 MB
[2025-10-03 17:03:42Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 17:03:45Z] Running in 64-bit process: True
[2025-10-03 17:03:45Z] 64-bit Operating System: True
[2025-10-03 17:03:45Z] Windows Forms support verified
[2025-10-03 17:03:45Z] === File System Check ===
[2025-10-03 17:03:45Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:03:45Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 17:03:45Z] Questions file found at expected location
[2025-10-03 17:03:45Z] Write permissions verified
[2025-10-03 17:03:45Z] === Application Initialization ===
[2025-10-03 17:03:45Z] Creating StartForm...
[2025-10-03 17:03:45Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 17:03:47Z] === Application Ending ===
[2025-10-03 17:03:47Z] End time: 03/10/2025 05:03:47 م
[2025-10-03 17:04:44Z] === Application Starting ===
[2025-10-03 17:04:44Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 17:04:44Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 17:04:44Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:04:44Z] UI Culture set to English (US)
[2025-10-03 17:04:44Z] === System Requirements Check ===
[2025-10-03 17:04:44Z] Available memory: 0 MB
[2025-10-03 17:04:44Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 17:04:48Z] Running in 64-bit process: True
[2025-10-03 17:04:48Z] 64-bit Operating System: True
[2025-10-03 17:04:48Z] Windows Forms support verified
[2025-10-03 17:04:48Z] === File System Check ===
[2025-10-03 17:04:48Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:04:48Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 17:04:48Z] Questions file found at expected location
[2025-10-03 17:04:48Z] Write permissions verified
[2025-10-03 17:04:48Z] === Application Initialization ===
[2025-10-03 17:04:48Z] Creating StartForm...
[2025-10-03 17:04:48Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 17:04:50Z] === Application Ending ===
[2025-10-03 17:04:50Z] End time: 03/10/2025 05:04:50 م
[2025-10-03 17:06:19Z] === Application Starting ===
[2025-10-03 17:06:19Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 17:06:19Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 17:06:19Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:06:19Z] UI Culture set to English (US)
[2025-10-03 17:06:19Z] === System Requirements Check ===
[2025-10-03 17:06:19Z] Available memory: 0 MB
[2025-10-03 17:06:19Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 17:06:21Z] Running in 64-bit process: True
[2025-10-03 17:06:21Z] 64-bit Operating System: True
[2025-10-03 17:06:21Z] Windows Forms support verified
[2025-10-03 17:06:21Z] === File System Check ===
[2025-10-03 17:06:21Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:06:21Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 17:06:21Z] Questions file found at expected location
[2025-10-03 17:06:21Z] Write permissions verified
[2025-10-03 17:06:21Z] === Application Initialization ===
[2025-10-03 17:06:21Z] Creating a simple test form...
[2025-10-03 17:06:22Z] Running the test form...
[2025-10-03 17:08:01Z] === Application Ending ===
[2025-10-03 17:08:01Z] End time: 03/10/2025 05:08:01 م
[2025-10-03 17:21:09Z] === Application Starting ===
[2025-10-03 17:21:09Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 17:21:09Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 17:21:09Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:21:09Z] UI Culture set to English (US)
[2025-10-03 17:21:09Z] === System Requirements Check ===
[2025-10-03 17:21:09Z] Available memory: 0 MB
[2025-10-03 17:21:09Z] Warning: Low memory detected. Application may run slowly.
[2025-10-03 17:21:11Z] Running in 64-bit process: True
[2025-10-03 17:21:11Z] 64-bit Operating System: True
[2025-10-03 17:21:11Z] Windows Forms support verified
[2025-10-03 17:21:11Z] === File System Check ===
[2025-10-03 17:21:11Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:21:11Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 17:21:11Z] Questions file found at expected location
[2025-10-03 17:21:11Z] Write permissions verified
[2025-10-03 17:21:11Z] === Application Initialization ===
[2025-10-03 17:21:11Z] Creating StartForm...
[2025-10-03 17:21:12Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 328
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 17:21:13Z] === Application Ending ===
[2025-10-03 17:21:13Z] End time: 03/10/2025 05:21:13 م
[2025-10-03 17:41:53Z] === Application Starting ===
[2025-10-03 17:41:53Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 17:41:53Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 17:41:53Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:41:53Z] UI Culture set to English (US)
[2025-10-03 17:41:53Z] === System Requirements Check ===
[2025-10-03 17:41:53Z] Available memory: 0 MB
[2025-10-03 17:41:53Z] Running in 64-bit process: True
[2025-10-03 17:41:53Z] 64-bit Operating System: True
[2025-10-03 17:41:54Z] Windows Forms support verified
[2025-10-03 17:41:54Z] === File System Check ===
[2025-10-03 17:41:54Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 17:41:54Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 17:41:54Z] Questions file found at expected location
[2025-10-03 17:41:54Z] Write permissions verified
[2025-10-03 17:41:54Z] === Application Initialization ===
[2025-10-03 17:41:54Z] Creating StartForm...
[2025-10-03 17:41:54Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 330
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 17:41:57Z] === Application Ending ===
[2025-10-03 17:41:57Z] End time: 03/10/2025 05:41:57 م
[2025-10-03 22:22:53Z] === Application Starting ===
[2025-10-03 22:22:53Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 22:22:53Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 22:22:53Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:22:53Z] Debug mode enabled via command line
[2025-10-03 22:22:53Z] UI Culture set to English (US)
[2025-10-03 22:22:53Z] === System Requirements Check ===
[2025-10-03 22:22:53Z] Available memory: 0 MB
[2025-10-03 22:22:53Z] Running in 64-bit process: True
[2025-10-03 22:22:53Z] 64-bit Operating System: True
[2025-10-03 22:22:53Z] Windows Forms support verified
[2025-10-03 22:22:53Z] === File System Check ===
[2025-10-03 22:22:53Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:22:53Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 22:22:53Z] Questions file found at expected location
[2025-10-03 22:22:53Z] Write permissions verified
[2025-10-03 22:22:53Z] === Application Initialization ===
[2025-10-03 22:22:53Z] Creating StartForm...
[2025-10-03 22:22:53Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 330
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 22:22:57Z] === Application Ending ===
[2025-10-03 22:22:57Z] End time: 03/10/2025 10:22:57 م
[2025-10-03 22:32:28Z] === Application Starting ===
[2025-10-03 22:32:28Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 22:32:28Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 22:32:28Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:32:28Z] Debug mode enabled via command line
[2025-10-03 22:32:29Z] UI Culture set to English (US)
[2025-10-03 22:32:29Z] === System Requirements Check ===
[2025-10-03 22:32:29Z] Available memory: 0 MB
[2025-10-03 22:32:29Z] Running in 64-bit process: True
[2025-10-03 22:32:29Z] 64-bit Operating System: True
[2025-10-03 22:32:29Z] Windows Forms support verified
[2025-10-03 22:32:29Z] === File System Check ===
[2025-10-03 22:32:29Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:32:29Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 22:32:29Z] Questions file found at expected location
[2025-10-03 22:32:29Z] Write permissions verified
[2025-10-03 22:32:29Z] === Application Initialization ===
[2025-10-03 22:32:29Z] Creating StartForm...
[2025-10-03 22:32:29Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 330
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 22:32:32Z] === Application Ending ===
[2025-10-03 22:32:32Z] End time: 03/10/2025 10:32:32 م
[2025-10-03 22:34:15Z] === Application Starting ===
[2025-10-03 22:34:15Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 22:34:15Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 22:34:15Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:34:15Z] UI Culture set to English (US)
[2025-10-03 22:34:15Z] === System Requirements Check ===
[2025-10-03 22:34:15Z] Available memory: 0 MB
[2025-10-03 22:34:15Z] Running in 64-bit process: True
[2025-10-03 22:34:15Z] 64-bit Operating System: True
[2025-10-03 22:34:15Z] Windows Forms support verified
[2025-10-03 22:34:15Z] === File System Check ===
[2025-10-03 22:34:15Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:34:15Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 22:34:15Z] Questions file found at expected location
[2025-10-03 22:34:15Z] Write permissions verified
[2025-10-03 22:34:15Z] === Application Initialization ===
[2025-10-03 22:34:15Z] Creating StartForm...
[2025-10-03 22:34:15Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 330
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 22:34:18Z] === Application Ending ===
[2025-10-03 22:34:18Z] End time: 03/10/2025 10:34:18 م
[2025-10-03 22:35:44Z] === Application Starting ===
[2025-10-03 22:35:44Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 22:35:44Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 22:35:44Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:35:44Z] UI Culture set to English (US)
[2025-10-03 22:35:44Z] === System Requirements Check ===
[2025-10-03 22:35:44Z] Available memory: 0 MB
[2025-10-03 22:35:44Z] Running in 64-bit process: True
[2025-10-03 22:35:44Z] 64-bit Operating System: True
[2025-10-03 22:35:44Z] Windows Forms support verified
[2025-10-03 22:35:44Z] === File System Check ===
[2025-10-03 22:35:44Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:35:44Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 22:35:44Z] Questions file found at expected location
[2025-10-03 22:35:44Z] Write permissions verified
[2025-10-03 22:35:44Z] === Application Initialization ===
[2025-10-03 22:35:44Z] Creating GameManager...
[2025-10-03 22:35:44Z] Creating StartForm...
[2025-10-03 22:35:44Z] ----- Exception -----
Message: ButtonBase controls do not support setting the BorderColor to Transparent.
Type: System.NotSupportedException
Source: System.Windows.Forms
StackTrace:    at System.Windows.Forms.FlatButtonAppearance.set_BorderColor(Color value)
   at MillionaireGame.StartForm.CreateStyledButton(String text, Point location, Color backColor) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 163
   at MillionaireGame.StartForm.InitializeComponents() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 93
   at MillionaireGame.StartForm..ctor(GameManager gm) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Forms\StartForm.cs:line 31
   at MillionaireGame.Program.RunApplication() in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 340
   at MillionaireGame.Program.Main(String[] args) in C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\Program.cs:line 115
---------------------

[2025-10-03 22:35:47Z] === Application Ending ===
[2025-10-03 22:35:47Z] End time: 03/10/2025 10:35:47 م
[2025-10-03 22:37:45Z] === Application Starting ===
[2025-10-03 22:37:45Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 22:37:45Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 22:37:45Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:37:45Z] UI Culture set to English (US)
[2025-10-03 22:37:45Z] === System Requirements Check ===
[2025-10-03 22:37:45Z] Available memory: 0 MB
[2025-10-03 22:37:45Z] Running in 64-bit process: True
[2025-10-03 22:37:45Z] 64-bit Operating System: True
[2025-10-03 22:37:45Z] Windows Forms support verified
[2025-10-03 22:37:45Z] === File System Check ===
[2025-10-03 22:37:45Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:37:45Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 22:37:45Z] Questions file found at expected location
[2025-10-03 22:37:45Z] Write permissions verified
[2025-10-03 22:37:45Z] === Application Initialization ===
[2025-10-03 22:37:45Z] Creating GameManager...
[2025-10-03 22:37:45Z] Creating StartForm...
[2025-10-03 22:37:45Z] Starting application main loop...
[2025-10-03 22:37:52Z] Using default game settings
[2025-10-03 22:38:51Z] Saving game result for player: Player_10032238, Amount: 0
[2025-10-03 22:38:51Z] Loaded 1 game results
[2025-10-03 22:38:51Z] Game result saved successfully
[2025-10-03 22:38:51Z] Loaded 2 game results
[2025-10-03 22:38:51Z] Achievement unlocked: First Steps
[2025-10-03 22:38:56Z] Loaded 2 game results
[2025-10-03 22:39:06Z] Application main loop ended normally.
[2025-10-03 22:39:06Z] === Application Ending ===
[2025-10-03 22:39:06Z] End time: 03/10/2025 10:39:06 م
[2025-10-03 22:42:54Z] === Application Starting ===
[2025-10-03 22:42:54Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 22:42:54Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 22:42:54Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:42:54Z] UI Culture set to English (US)
[2025-10-03 22:42:54Z] === System Requirements Check ===
[2025-10-03 22:42:54Z] Available memory: 0 MB
[2025-10-03 22:42:54Z] Running in 64-bit process: True
[2025-10-03 22:42:54Z] 64-bit Operating System: True
[2025-10-03 22:42:54Z] Windows Forms support verified
[2025-10-03 22:42:54Z] === File System Check ===
[2025-10-03 22:42:54Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 22:42:54Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 22:42:54Z] Questions file found at expected location
[2025-10-03 22:42:54Z] Write permissions verified
[2025-10-03 22:42:54Z] === Application Initialization ===
[2025-10-03 22:42:54Z] Creating GameManager...
[2025-10-03 22:42:54Z] Creating StartForm...
[2025-10-03 22:42:54Z] Starting application main loop...
[2025-10-03 22:42:58Z] Using default game settings
[2025-10-03 22:43:30Z] Application main loop ended normally.
[2025-10-03 22:43:30Z] === Application Ending ===
[2025-10-03 22:43:30Z] End time: 03/10/2025 10:43:30 م
[2025-10-03 23:08:13Z] === Application Starting ===
[2025-10-03 23:08:13Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-03 23:08:13Z] Working Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread
[2025-10-03 23:08:13Z] Base Directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 23:08:13Z] UI Culture set to English (US)
[2025-10-03 23:08:14Z] === System Requirements Check ===
[2025-10-03 23:08:14Z] Available memory: 0 MB
[2025-10-03 23:08:14Z] Running in 64-bit process: True
[2025-10-03 23:08:14Z] 64-bit Operating System: True
[2025-10-03 23:08:14Z] Windows Forms support verified
[2025-10-03 23:08:14Z] === File System Check ===
[2025-10-03 23:08:14Z] Application directory: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\
[2025-10-03 23:08:14Z] Looking for questions file at: C:\Users\<USER>\Desktop\MillionaireGame_NoLinqNoThread\bin\Debug\net6.0-windows\questions.txt
[2025-10-03 23:08:14Z] Questions file found at expected location
[2025-10-03 23:08:14Z] Write permissions verified
[2025-10-03 23:08:14Z] === Application Initialization ===
[2025-10-03 23:08:14Z] Creating GameManager...
[2025-10-03 23:08:14Z] Creating StartForm...
[2025-10-03 23:08:14Z] Starting application main loop...
[2025-10-03 23:08:27Z] Using default game settings
[2025-10-03 23:08:43Z] Loaded 2 game results
[2025-10-03 23:09:34Z] Application main loop ended normally.
[2025-10-03 23:09:34Z] === Application Ending ===
[2025-10-03 23:09:34Z] End time: 03/10/2025 11:09:34 م
[2025-10-05 20:30:50Z] === Application Starting ===
[2025-10-05 20:30:50Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-05 20:30:50Z] Working Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24
[2025-10-05 20:30:50Z] Base Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-05 20:30:50Z] UI Culture set to English (US)
[2025-10-05 20:30:50Z] === System Requirements Check ===
[2025-10-05 20:30:50Z] Available memory: 0 MB
[2025-10-05 20:30:50Z] Running in 64-bit process: True
[2025-10-05 20:30:50Z] 64-bit Operating System: True
[2025-10-05 20:30:50Z] Windows Forms support verified
[2025-10-05 20:30:50Z] === File System Check ===
[2025-10-05 20:30:50Z] Application directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-05 20:30:50Z] Looking for questions file at: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\questions.txt
[2025-10-05 20:30:50Z] Questions file found at expected location
[2025-10-05 20:30:50Z] Write permissions verified
[2025-10-05 20:30:50Z] === Application Initialization ===
[2025-10-05 20:30:50Z] Creating GameManager...
[2025-10-05 20:30:50Z] Creating StartForm...
[2025-10-05 20:30:50Z] Starting application main loop...
[2025-10-05 20:30:53Z] Using default game settings
[2025-10-05 20:31:23Z] Application main loop ended normally.
[2025-10-05 20:31:24Z] === Application Ending ===
[2025-10-05 20:31:24Z] End time: 05/10/2025 08:31:24 م
[2025-10-05 20:50:10Z] === Application Starting ===
[2025-10-05 20:50:10Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-05 20:50:10Z] Working Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24
[2025-10-05 20:50:10Z] Base Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-05 20:50:10Z] UI Culture set to English (US)
[2025-10-05 20:50:10Z] === System Requirements Check ===
[2025-10-05 20:50:10Z] Available memory: 0 MB
[2025-10-05 20:50:10Z] Running in 64-bit process: True
[2025-10-05 20:50:10Z] 64-bit Operating System: True
[2025-10-05 20:50:10Z] Windows Forms support verified
[2025-10-05 20:50:10Z] === File System Check ===
[2025-10-05 20:50:10Z] Application directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-05 20:50:10Z] Looking for questions file at: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\questions.txt
[2025-10-05 20:50:10Z] Questions file found at expected location
[2025-10-05 20:50:10Z] Write permissions verified
[2025-10-05 20:50:10Z] === Application Initialization ===
[2025-10-05 20:50:10Z] Creating GameManager...
[2025-10-05 20:50:10Z] Creating StartForm...
[2025-10-05 20:50:10Z] Starting application main loop...
[2025-10-05 20:50:20Z] Using default game settings
[2025-10-05 20:50:35Z] Loaded 2 game results
[2025-10-05 20:50:42Z] Loaded 2 game results
[2025-10-05 20:50:58Z] Application main loop ended normally.
[2025-10-05 20:50:58Z] === Application Ending ===
[2025-10-05 20:50:58Z] End time: 05/10/2025 08:50:58 م
[2025-10-05 20:51:37Z] === Application Starting ===
[2025-10-05 20:51:37Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-05 20:51:37Z] Working Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24
[2025-10-05 20:51:37Z] Base Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-05 20:51:37Z] UI Culture set to English (US)
[2025-10-05 20:51:37Z] === System Requirements Check ===
[2025-10-05 20:51:37Z] Available memory: 0 MB
[2025-10-05 20:51:37Z] Running in 64-bit process: True
[2025-10-05 20:51:37Z] 64-bit Operating System: True
[2025-10-05 20:51:37Z] Windows Forms support verified
[2025-10-05 20:51:37Z] === File System Check ===
[2025-10-05 20:51:37Z] Application directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-05 20:51:37Z] Looking for questions file at: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\questions.txt
[2025-10-05 20:51:37Z] Questions file found at expected location
[2025-10-05 20:51:37Z] Write permissions verified
[2025-10-05 20:51:37Z] === Application Initialization ===
[2025-10-05 20:51:37Z] Creating GameManager...
[2025-10-05 20:51:37Z] Creating StartForm...
[2025-10-05 20:51:37Z] Starting application main loop...
[2025-10-05 20:51:40Z] Application main loop ended normally.
[2025-10-05 20:51:40Z] === Application Ending ===
[2025-10-05 20:51:40Z] End time: 05/10/2025 08:51:40 م
[2025-10-05 20:54:29Z] === Application Starting ===
[2025-10-05 20:54:29Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-05 20:54:29Z] Working Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24
[2025-10-05 20:54:29Z] Base Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-05 20:54:29Z] UI Culture set to English (US)
[2025-10-05 20:54:29Z] === System Requirements Check ===
[2025-10-05 20:54:29Z] Available memory: 0 MB
[2025-10-05 20:54:29Z] Running in 64-bit process: True
[2025-10-05 20:54:29Z] 64-bit Operating System: True
[2025-10-05 20:54:29Z] Windows Forms support verified
[2025-10-05 20:54:29Z] === File System Check ===
[2025-10-05 20:54:29Z] Application directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-05 20:54:29Z] Looking for questions file at: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\questions.txt
[2025-10-05 20:54:29Z] Questions file found at expected location
[2025-10-05 20:54:29Z] Write permissions verified
[2025-10-05 20:54:29Z] === Application Initialization ===
[2025-10-05 20:54:29Z] Creating GameManager...
[2025-10-05 20:54:29Z] Creating StartForm...
[2025-10-05 20:54:29Z] Starting application main loop...
[2025-10-05 20:54:32Z] Using default game settings
[2025-10-05 20:54:43Z] Application main loop ended normally.
[2025-10-05 20:54:43Z] === Application Ending ===
[2025-10-05 20:54:43Z] End time: 05/10/2025 08:54:43 م
[2025-10-08 21:10:20Z] === Application Starting ===
[2025-10-08 21:10:20Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-08 21:10:20Z] Working Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24
[2025-10-08 21:10:20Z] Base Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-08 21:10:20Z] UI Culture set to English (US)
[2025-10-08 21:10:20Z] === System Requirements Check ===
[2025-10-08 21:10:20Z] Available memory: 0 MB
[2025-10-08 21:10:20Z] Running in 64-bit process: True
[2025-10-08 21:10:20Z] 64-bit Operating System: True
[2025-10-08 21:10:20Z] Windows Forms support verified
[2025-10-08 21:10:20Z] === File System Check ===
[2025-10-08 21:10:20Z] Application directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-08 21:10:20Z] Looking for questions file at: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\questions.txt
[2025-10-08 21:10:20Z] Questions file found at expected location
[2025-10-08 21:10:20Z] Write permissions verified
[2025-10-08 21:10:20Z] === Application Initialization ===
[2025-10-08 21:10:20Z] Creating GameManager...
[2025-10-08 21:10:20Z] Creating StartForm...
[2025-10-08 21:10:20Z] Starting application main loop...
[2025-10-08 21:11:32Z] Application main loop ended normally.
[2025-10-08 21:11:32Z] === Application Ending ===
[2025-10-08 21:11:33Z] End time: 08/10/2025 09:11:32 م
[2025-10-09 11:43:47Z] === Application Starting ===
[2025-10-09 11:43:47Z] Runtime: .NET 6.0.30
CLR Version: 6.0.30
OS: Microsoft Windows 10.0.19045
Process: 64-bit
[2025-10-09 11:43:47Z] Working Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24
[2025-10-09 11:43:47Z] Base Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-09 11:43:47Z] UI Culture set to en-US
[2025-10-09 11:43:47Z] === System Requirements Check ===
[2025-10-09 11:43:47Z] Available memory: 0 MB
[2025-10-09 11:43:47Z] 64-bit process: True
[2025-10-09 11:43:47Z] 64-bit OS: True
[2025-10-09 11:43:47Z] Windows Forms OK
[2025-10-09 11:43:47Z] === File System Check ===
[2025-10-09 11:43:47Z] App directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-09 11:43:47Z] Questions file: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\questions.txt
[2025-10-09 11:43:47Z] Questions file found
[2025-10-09 11:43:47Z] Write permissions OK
[2025-10-09 11:43:47Z] === Application Init ===
[2025-10-09 11:43:47Z] Creating game control
[2025-10-09 11:43:47Z] Creating start form
[2025-10-09 11:43:47Z] Starting main loop
[2025-10-09 11:45:06Z] Saving game result for player: Player_10091145, Amount: 1000
[2025-10-09 11:45:06Z] Game result saved successfully
[2025-10-09 11:45:06Z] Loaded 1 game results
[2025-10-09 11:45:06Z] Achievement unlocked: First Steps
[2025-10-09 11:45:06Z] Achievement unlocked: First Victory
[2025-10-09 11:45:06Z] Achievement unlocked: Thousand Club
[2025-10-09 11:45:06Z] Achievement unlocked: Speed Demon
[2025-10-09 11:45:21Z] App ended normally
[2025-10-09 11:45:21Z] === Application Ending ===
[2025-10-09 11:45:21Z] End time: 09/10/2025 11:45:21 ص
[2025-10-09 13:09:25Z] === Application Starting ===
[2025-10-09 13:09:25Z] MillionaireGame - Educational Edition
[2025-10-09 13:09:25Z] Working Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24
[2025-10-09 13:09:25Z] Base Directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-09 13:09:25Z] Application language: English (default)
[2025-10-09 13:09:25Z] === System Requirements Check ===
[2025-10-09 13:09:25Z] Available memory: 0 MB
[2025-10-09 13:09:25Z] System: Windows
[2025-10-09 13:09:25Z] .NET Framework: Compatible
[2025-10-09 13:09:25Z] Windows Forms OK
[2025-10-09 13:09:25Z] === File System Check ===
[2025-10-09 13:09:25Z] App directory: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-09 13:09:25Z] Questions file: C:\Users\<USER>\Desktop\ITE_BPG402_C10_F24\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\questions.txt
[2025-10-09 13:09:25Z] Questions file found
[2025-10-09 13:09:25Z] Write permissions OK
[2025-10-09 13:09:25Z] === Application Init ===
[2025-10-09 13:09:25Z] Creating game control
[2025-10-09 13:09:25Z] Creating start form
[2025-10-09 13:09:25Z] Starting main loop
[2025-10-09 13:09:51Z] App ended normally
[2025-10-09 13:09:51Z] === Application Ending ===
[2025-10-09 13:09:51Z] End time: 09/10/2025 01:09:51 م
[2025-10-09 14:47:15Z] === Application Starting ===
[2025-10-09 14:47:15Z] MillionaireGame v1.0
[2025-10-09 14:47:15Z] Working Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24
[2025-10-09 14:47:15Z] Base Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-09 14:47:16Z] Application language: English (default)
[2025-10-09 14:47:16Z] === System Requirements Check ===
[2025-10-09 14:47:16Z] Available memory: 0 MB
[2025-10-09 14:47:16Z] System: Windows
[2025-10-09 14:47:16Z] .NET Framework: Compatible
[2025-10-09 14:47:16Z] Windows Forms OK
[2025-10-09 14:47:16Z] === File System Check ===
[2025-10-09 14:47:16Z] App directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
[2025-10-09 14:47:16Z] Questions file: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\questions.txt
[2025-10-09 14:47:16Z] Questions file found
[2025-10-09 14:47:16Z] Write permissions OK
[2025-10-09 14:47:16Z] === Application Init ===
[2025-10-09 14:47:16Z] Creating game control
[2025-10-09 14:47:16Z] Creating start form
[2025-10-09 14:47:16Z] Starting main loop
[2025-10-09 14:47:18Z] Loaded 1 game results
[2025-10-09 14:47:21Z] Loaded 1 game results
[2025-10-09 14:47:31Z] Loaded 1 game results
[2025-10-09 14:48:01Z] Loaded 1 game results
[2025-10-09 14:48:05Z] Using default game settings
[2025-10-09 14:48:20Z] App ended normally
[2025-10-09 14:48:20Z] === Application Ending ===
[2025-10-09 14:48:20Z] End time: 09/10/2025 02:48:20 م
09/10/2025 03:14:13 م === Application Starting ===
09/10/2025 03:14:13 م MillionaireGame v1.0
09/10/2025 03:14:13 م Working Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24
09/10/2025 03:14:13 م Base Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
09/10/2025 03:14:13 م Application language: English (default)
09/10/2025 03:30:32 م === Application Starting ===
09/10/2025 03:30:32 م MillionaireGame v1.0
09/10/2025 03:30:32 م Working Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24
09/10/2025 03:30:32 م Base Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
09/10/2025 03:30:32 م Application language: English (default)
09/10/2025 03:42:40 م === Application Starting ===
09/10/2025 03:42:40 م MillionaireGame v1.0
09/10/2025 03:42:40 م Working Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24
09/10/2025 03:42:40 م Base Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
09/10/2025 03:42:40 م Application language: English (default)
09/10/2025 03:42:44 م Loaded 1 game results
09/10/2025 03:42:50 م Loaded 1 game results
09/10/2025 03:43:07 م Loaded 1 game results
09/10/2025 03:43:18 م === Application Starting ===
09/10/2025 03:43:18 م MillionaireGame v1.0
09/10/2025 03:43:18 م Working Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24
09/10/2025 03:43:18 م Base Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
09/10/2025 03:43:18 م Application language: English (default)
09/10/2025 03:43:21 م Loaded 1 game results
09/10/2025 03:43:29 م Loaded 1 game results
09/10/2025 03:43:34 م Using default game settings
09/10/2025 03:43:59 م === Application Starting ===
09/10/2025 03:43:59 م MillionaireGame v1.0
09/10/2025 03:43:59 م Working Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24
09/10/2025 03:43:59 م Base Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
09/10/2025 03:43:59 م Application language: English (default)
09/10/2025 03:44:08 م === Application Ending ===
09/10/2025 03:44:08 م End time: 09/10/2025 03:44:08 م
09/10/2025 03:44:59 م === Application Starting ===
09/10/2025 03:44:59 م MillionaireGame v1.0
09/10/2025 03:44:59 م Working Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24
09/10/2025 03:44:59 م Base Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
09/10/2025 03:44:59 م Application language: English (default)
09/10/2025 03:45:18 م === Application Ending ===
09/10/2025 03:45:18 م End time: 09/10/2025 03:45:18 م
09/10/2025 03:45:41 م === Application Starting ===
09/10/2025 03:45:42 م MillionaireGame v1.0
09/10/2025 03:45:42 م Working Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24
09/10/2025 03:45:42 م Base Directory: C:\Users\<USER>\Desktop\مجلد جديد\ITE_BPG402_C10_F24\bin\Debug\net6.0-windows\
09/10/2025 03:45:42 م Application language: English (default)
09/10/2025 03:46:14 م === Application Ending ===
09/10/2025 03:46:14 م End time: 09/10/2025 03:46:14 م
