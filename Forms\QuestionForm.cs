using System;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Drawing;
using MillionaireGame.Classes;
using System.Text; // unused - student mistake

namespace MillionaireGame
{
    public class QuestionForm : Form
    {
        private MainGameControl _gm;
        private Question? _currentQuestion;
        private int _currentLevel = 1;
        private bool used5050 = false;
        private bool usedSwitch = false;
        private int guaranteedAmount = 0;
        private DateTime _gameStartTime;

        // unusd variabels - studnet mistke
        private bool isGameActive = true;
        private string playerName = "";
        private int totalScore = 0;
        private bool soundEnabled = true;
        private int timeLeft = 30;

        private ProgressBar? progressLevel;
        private Label? lblProgress;

        private Label? lblLevel;
        private Label? lblQuestion;
        private RadioButton[] rdoOptions = new RadioButton[4];
        private Button? btnConfirm;
        private Button? btnNext;
        private Button? btn5050;
        private Button? btnSwitch;
        private Button? btnWithdraw;
        private ListBox? prizeList;
        private bool _answerConfirmed = false;

        private readonly string[] PrizeLadder = {
            "1000000", "500000", "250000", "125000", "64000", "32000", "16000", "8000", "4000", "2000", "1000", "500", "300", "200", "100", "0"
        };

        public QuestionForm(MainGameControl gm)
        {
            _gm = gm ?? throw new ArgumentNullException(nameof(gm));
            _gameStartTime = DateTime.Now;

            // initialize form
            InitializeComponents();
            LoadNewQuestion();
        }

        private void InitializeComponents()
        {
            this.Text = "Who Wants to Be a Millionaire - من سيربح المليون";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(15, 25, 45);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            var mainPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(1000, 700),
                BackColor = Color.FromArgb(15, 25, 45)
            };

            // Progress bar for level
            progressLevel = new ProgressBar
            {
                Location = new Point(20, 20),
                Size = new Size(300, 25),
                Minimum = 1,
                Maximum = 15,
                Value = 1,
                Style = ProgressBarStyle.Continuous,
                ForeColor = Color.Gold,
                BackColor = Color.FromArgb(40, 50, 70)
            };

            lblProgress = new Label
            {
                Location = new Point(330, 20),
                Size = new Size(120, 25),
                Font = new Font("Arial", 11, FontStyle.Bold),
                ForeColor = Color.Gold,
                Text = "Level 1/15",
                TextAlign = ContentAlignment.MiddleLeft
            };

            // Level and amount label
            lblLevel = new Label
            {
                Location = new Point(20, 60),
                Size = new Size(750, 35),
                Font = new Font("Arial", 14, FontStyle.Bold),
                ForeColor = Color.Gold,
                Text = "Level: 1",
                TextAlign = ContentAlignment.MiddleLeft,
                BackColor = Color.FromArgb(25, 35, 55),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Question label with better styling
            lblQuestion = new Label
            {
                Location = new Point(20, 110),
                Size = new Size(750, 100),
                Font = new Font("Arial", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Text = "Loading question...",
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(30, 40, 60),
                BorderStyle = BorderStyle.FixedSingle
            };

            // Options with better styling
            string[] optionLabels = { "A:", "B:", "C:", "D:" };
            for (int i = 0; i < 4; i++)
            {
                rdoOptions[i] = new RadioButton
                {
                    Location = new Point(20, 230 + i * 50),
                    Size = new Size(750, 40),
                    Font = new Font("Arial", 14, FontStyle.Regular),
                    ForeColor = Color.White,
                    Text = $"{optionLabels[i]} Loading option...",
                    AutoSize = false,
                    BackColor = Color.FromArgb(35, 45, 65),
                    FlatStyle = FlatStyle.Flat,
                    Appearance = Appearance.Normal,
                    CheckAlign = ContentAlignment.MiddleLeft,
                    TextAlign = ContentAlignment.MiddleLeft
                };

                // Add hover effect using regular event handlers
                rdoOptions[i].MouseEnter += RadioOption_MouseEnter;
                rdoOptions[i].MouseLeave += RadioOption_MouseLeave;
            }

            // Buttons panel
            var buttonsPanel = new Panel
            {
                Location = new Point(20, 450),
                Size = new Size(750, 60),
                BackColor = Color.FromArgb(20, 30, 50)
            };

            // Confirm button
            btnConfirm = new Button
            {
                Location = new Point(10, 10),
                Size = new Size(120, 40),
                Text = "✓ Confirm Answer",
                Font = new Font("Arial", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 150, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnConfirm.FlatAppearance.BorderSize = 0;
            btnConfirm.Click += new EventHandler(BtnConfirm_Click);

            // 50/50 button
            btn5050 = new Button
            {
                Location = new Point(150, 10),
                Size = new Size(100, 40),
                Text = "🎯 50:50",
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(255, 140, 0),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btn5050.FlatAppearance.BorderSize = 0;
            btn5050.Click += new EventHandler(Btn5050_Click);

            // Switch button
            btnSwitch = new Button
            {
                Location = new Point(270, 10),
                Size = new Size(120, 40),
                Text = "🔄 Switch Question",
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 150, 200),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnSwitch.FlatAppearance.BorderSize = 0;
            btnSwitch.Click += new EventHandler(BtnSwitch_Click);

            // Withdraw button
            btnWithdraw = new Button
            {
                Location = new Point(410, 10),
                Size = new Size(120, 40),
                Text = "💰 Withdraw",
                Font = new Font("Arial", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            btnWithdraw.FlatAppearance.BorderSize = 0;
            btnWithdraw.Click += new EventHandler(BtnWithdraw_Click);

            btnNext = new Button
            {
                Location = new Point(550, 10),
                Size = new Size(120, 40),
                Text = "➡️ Next Question",
                Font = new Font("Arial", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                Enabled = false  // Disabled until answer is confirmed
            };
            btnNext.FlatAppearance.BorderSize = 0;
            btnNext.Click += new EventHandler(BtnNext_Click);

            // Add buttons to panel
            buttonsPanel.Controls.Add(btnConfirm);
            buttonsPanel.Controls.Add(btn5050);
            buttonsPanel.Controls.Add(btnSwitch);
            buttonsPanel.Controls.Add(btnWithdraw);
            buttonsPanel.Controls.Add(btnNext);

            // Prize ladder with better styling
            prizeList = new ListBox
            {
                Location = new Point(800, 60),
                Size = new Size(180, 450),
                Font = new Font("Arial", 11, FontStyle.Bold),
                ForeColor = Color.Gold,
                BackColor = Color.FromArgb(25, 35, 55),
                BorderStyle = BorderStyle.FixedSingle,
                SelectionMode = SelectionMode.None
            };

            // Add formatted prize ladder
            for (int i = 0; i < PrizeLadder.Length; i++)
            {
                string prize = PrizeLadder[i];
                string formatted = $"{15 - i:D2}. ${int.Parse(prize):N0}";
                prizeList.Items.Add(formatted);
            }

            mainPanel.Controls.Add(progressLevel);
            mainPanel.Controls.Add(lblProgress);
            mainPanel.Controls.Add(lblLevel);
            mainPanel.Controls.Add(lblQuestion);
            foreach (var rdo in rdoOptions)
                mainPanel.Controls.Add(rdo);
            mainPanel.Controls.Add(buttonsPanel);
            mainPanel.Controls.Add(prizeList);

            // Add main panel to form
            this.Controls.Add(mainPanel);
        }

        private void LoadNewQuestion()
        {
            _currentQuestion = _gm.GetRandomQuestion(_currentLevel);
            if (_currentQuestion == null || string.IsNullOrEmpty(_currentQuestion.Text))
            {
                MessageBox.Show("No questions available for this level.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                ShowResult("0");
                return;
            }

            string currentAmount = PrizeLadder[Math.Max(0, Math.Min(PrizeLadder.Length - _currentLevel, PrizeLadder.Length - 1))];
            if (lblLevel != null) lblLevel.Text = $"Level {_currentLevel} | Current: ${int.Parse(currentAmount):N0} | Guaranteed: ${guaranteedAmount:N0}";
            if (lblQuestion != null) lblQuestion.Text = _currentQuestion.Text;

            if (progressLevel != null) progressLevel.Value = _currentLevel;
            if (lblProgress != null) lblProgress.Text = $"Level {_currentLevel}/15";

            _answerConfirmed = false;
            if (btnNext != null) btnNext.Enabled = false;
            if (btnConfirm != null) btnConfirm.Enabled = true;

            var opts = _gm.GetShuffledAnswers(_currentQuestion);
            string[] optionLabels = { "A:", "B:", "C:", "D:" };

            for (int i = 0; i < 4; i++)
            {
                rdoOptions[i].Enabled = true;
                rdoOptions[i].Visible = true;
                rdoOptions[i].Checked = false;
                rdoOptions[i].BackColor = Color.FromArgb(35, 45, 65);

                if (i < opts.Count && !string.IsNullOrEmpty(opts[i]))
                {
                    rdoOptions[i].Text = $"{optionLabels[i]} {opts[i]}";
                }
                else
                {
                    rdoOptions[i].Text = $"{optionLabels[i]} (No Option)";
                    rdoOptions[i].Enabled = false;
                }
            }

            // Highlight current level in prize list (skip since SelectionMode is None)
            // if (prizeList != null && _currentLevel <= 15)
            // {
            //     prizeList.SelectedIndex = 15 - _currentLevel;
            // }

            // Reset lifeline buttons if they were used
            if (used5050)
            {
                btn5050!.Enabled = false;
                btn5050.BackColor = Color.Gray;
            }
            if (usedSwitch)
            {
                if (btnSwitch != null)
                {
                    btnSwitch.Enabled = false;
                    btnSwitch.BackColor = Color.Gray;
                }
            }
        }

        // duplicat funtion - studnet mistke
        private void LoadQuestion()
        {
            _currentQuestion = _gm.GetRandomQuestion(_currentLevel);
            if (_currentQuestion == null)
            {
                MessageBox.Show("No questons!", "Eror", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            lblQuestion.Text = _currentQuestion.Text;

            var answers = _gm.GetShuffledAnswers(_currentQuestion);
            rdoOptions[0].Text = "A: " + answers[0];
            rdoOptions[1].Text = "B: " + answers[1];
            rdoOptions[2].Text = "C: " + answers[2];
            rdoOptions[3].Text = "D: " + answers[3];

            for (int i = 0; i < 4; i++)
            {
                rdoOptions[i].Checked = false;
                rdoOptions[i].BackColor = Color.LightBlue;
            }

            // memry managment problm - creatng Graphics witout disposng
            Graphics g = this.CreateGraphics();
            Brush brush = new SolidBrush(Color.Red);
            // g.Dispose(); - comentd on purpos
            // brush.Dispose(); - comentd on purpos
        }

        // timer functions - maybe add later

        // This handles the 50:50 lifeline
        // When clicked:
        // - Keeps the correct answer
        // - Keeps one wrong answer
        // - Hides two wrong answers
        // Can only be used once per game
        private void Btn5050_Click(object? sender, EventArgs e)
        {
            // Don't do anything if already used or no question
            if (used5050 || _currentQuestion == null) return;

            // Make a list of the wrong answer buttons
            var wrongRadios = new List<RadioButton>();
            foreach (var rdo in rdoOptions)
            {
                // Extract the answer text without the prefix (A:, B:, C:, D:)
                string optionText = rdo.Text;
                if (optionText.Length > 3 && optionText[1] == ':')
                {
                    optionText = optionText.Substring(3).Trim();
                }

                if (!string.Equals(optionText, _currentQuestion.CorrectAnswer, StringComparison.OrdinalIgnoreCase))
                    wrongRadios.Add(rdo);
            }

            if (wrongRadios.Count <= 1) return;

            var rand = new Random();
            int removed = 0;
            while (removed < 2 && wrongRadios.Count > 0)
            {
                int idx = rand.Next(wrongRadios.Count);
                wrongRadios[idx].Enabled = false;
                wrongRadios.RemoveAt(idx);
                removed++;
            }

            used5050 = true;
            if (btn5050 != null) btn5050.Enabled = false;
        }

        // This handles the Switch Question lifeline
        // When clicked:
        // - Tries to find a different question of the same level
        // - Replaces the current question with the new one
        // - Can only be used once per game
        private void BtnSwitch_Click(object? sender, EventArgs e)
        {
            // Don't do anything if already used or no question
            if (usedSwitch || _currentQuestion == null) return;

            // Try to find a different question
            Question? newQ = null;
            for (int tries = 0; tries < 8; tries++)
            {
                var cand = _gm.GetRandomQuestion(_currentLevel);
                if (cand != null && cand.Text != _currentQuestion.Text)
                {
                    newQ = cand;
                    break;
                }
            }

            if (newQ == null)
            {
                MessageBox.Show("No alternative question available now.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            _currentQuestion = newQ;
            LoadNewQuestion();
            usedSwitch = true;
            if (btnSwitch != null) btnSwitch.Enabled = false;
        }

        // This handles when the player clicks Confirm Answer
        // It:
        // - Checks if the answer is correct
        // - Shows appropriate message
        // - Moves to next level or ends game
        // - Updates guaranteed money amount
        private void BtnConfirm_Click(object? sender, EventArgs e)
        {

            // Find which answer was selected
            RadioButton? selected = null;
            foreach (var rdo in rdoOptions)
            {
                if (rdo.Checked)
                {
                    selected = rdo;
                    break;
                }
            }

            if (selected == null)
            {
                MessageBox.Show("Please select an answer.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var confirm = MessageBox.Show("Are you sure about your answer?", "Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (confirm != DialogResult.Yes) return;

            // Extract the answer text without the prefix (A:, B:, C:, D:)
            string chosen = selected.Text;
            if (chosen.Length > 3 && chosen[1] == ':')
            {
                chosen = chosen.Substring(3).Trim(); // Remove "A: " or "B: " etc.
            }
            if (_currentQuestion == null) return;
            bool ok = _currentQuestion.IsCorrect(chosen);

            // Mark answer as confirmed
            _answerConfirmed = true;
            if (btnConfirm != null) btnConfirm.Enabled = false;

            if (ok)
            {
                MessageBox.Show("✅ Correct answer!", "Congratulations", MessageBoxButtons.OK, MessageBoxIcon.Information);
                _currentLevel++;

                // Update guaranteed amount every 5 levels
                if ((_currentLevel - 1) % 5 == 0 && _currentLevel - 1 > 0)
                {
                    guaranteedAmount = int.Parse(PrizeLadder[PrizeLadder.Length - _currentLevel]);
                }

                if (_currentLevel > 15)
                {
                    MessageBox.Show("🎉 CONGRATULATIONS! YOU WON THE MILLION! 🎉", "MILLIONAIRE!", MessageBoxButtons.OK, MessageBoxIcon.Exclamation);
                    ShowResult(PrizeLadder[0]); // Winner
                    return;
                }

                // Enable Next button instead of loading immediately
                if (btnNext != null) btnNext.Enabled = true;
            }
            else
            {
                MessageBox.Show("❌ Wrong answer! Game over.", "Game Over", MessageBoxButtons.OK, MessageBoxIcon.Error);
                ShowResult(guaranteedAmount.ToString());
            }
        }

        // New button handler for Next question - replaces automatic timer progression
        private void BtnNext_Click(object? sender, EventArgs e)
        {
            if (!_answerConfirmed)
            {
                MessageBox.Show("Please confirm your answer first.", "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            LoadNewQuestion();
        }

        private void BtnWithdraw_Click(object? sender, EventArgs e)
        {
            string currentWinnings = "0";
            if (_currentLevel > 1)
            {
                currentWinnings = PrizeLadder[PrizeLadder.Length - _currentLevel];
            }

            var confirm = MessageBox.Show($"Are you sure you want to withdraw and take ${int.Parse(currentWinnings):N0} as your winnings?", "💰 Withdraw", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (confirm == DialogResult.Yes)
            {
                ShowResult(currentWinnings);
            }
        }

        private void ShowResult(string amount)
        {
            // not usng usng statment - memry managment problm
            FileStream fs = new FileStream("game_result.txt", FileMode.Create);
            StreamWriter writer = new StreamWriter(fs);

            // writng reslt witout disposng resourcs
            writer.WriteLine("Game Result: " + amount);
            writer.WriteLine("Level: " + _currentLevel);
            writer.WriteLine("Date: " + DateTime.Now);

            // forgetng to clos fils - memry lak
            // fs.Close(); - comentd on purpos
            // writer.Close(); - comentd on purpos

            var gameDuration = DateTime.Now - _gameStartTime;
            var rf = new ResultForm(amount, _currentLevel, gameDuration);
            // wen reslt form closs, we clos questn form
            rf.FormClosed += ResultForm_FormClosed;
            rf.Show();
            this.Hide();
        }

        private void ResultForm_FormClosed(object? sender, FormClosedEventArgs e)
        {
            this.Close();
        }

        private void RadioOption_MouseEnter(object? sender, EventArgs e)
        {
            if (sender is RadioButton rdo && rdo.Enabled)
            {
                rdo.BackColor = Color.FromArgb(50, 60, 80);
            }
        }

        private void RadioOption_MouseLeave(object? sender, EventArgs e)
        {
            if (sender is RadioButton rdo && rdo.Enabled)
            {
                rdo.BackColor = Color.FromArgb(35, 45, 65);
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            base.OnFormClosed(e);
        }
    }
}















