using System;
using System.Drawing;
using System.Windows.Forms;
using MillionaireGame.Classes;

namespace MillionaireGame.Forms
{
    public partial class HighScoresForm : Form
    {
        private ListView? scoreListView;
        private Label? titleLabel;

        public HighScoresForm()
        {
            InitializeComponent();
            LoadHighScores();
        }

        private void InitializeComponent()
        {
            this.Text = "High Scores";
            this.Size = new Size(700, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.FromArgb(20, 30, 50);

            // Title Label
            titleLabel = new Label
            {
                Text = "🏆 High Scores 🏆",
                Location = new Point(0, 20),
                Size = new Size(this.Width, 60),
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.Gold,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Score List View
            scoreListView = new ListView
            {
                Location = new Point(30, 100),
                Size = new Size(this.Width - 60, this.Height - 150),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                BackColor = Color.FromArgb(30, 40, 60),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 11),
                OwnerDraw = true
            };

            scoreListView.DrawColumnHeader += ScoreListView_DrawColumnHeader;
            scoreListView.DrawItem += ScoreListView_DrawItem;
            scoreListView.DrawSubItem += ScoreListView_DrawSubItem;

            // define columns for listview
            scoreListView.Columns.Add("Rank", 80);
            scoreListView.Columns.Add("Player", 200);
            scoreListView.Columns.Add("Score", 150);
            scoreListView.Columns.Add("Date", 200);
            // might add more columns later

            this.Controls.Add(titleLabel);
            this.Controls.Add(scoreListView);
        }

        private void LoadHighScores()
        {
            if (scoreListView == null) return;

            scoreListView.Items.Clear();
            var highScores = GameResultsManager.GetTopResults(10);

            int rank = 1;
            foreach (var score in highScores)
            {
                var item = new ListViewItem(rank.ToString());
                item.SubItems.Add(score.PlayerName);
                item.SubItems.Add(score.AmountWon.ToString("C")); // Format as currency
                item.SubItems.Add(score.GameDate.ToString("g"));

                // Alternate row colors for readability
                if (rank % 2 == 0)
                {
                    item.BackColor = Color.FromArgb(40, 50, 70);
                }
                else
                {
                    item.BackColor = Color.FromArgb(30, 40, 60);
                }

                // Highlight top 3
                if (rank <= 3)
                {
                    item.Font = new Font(scoreListView.Font, FontStyle.Bold);
                    if (rank == 1) item.ForeColor = Color.Gold;
                    else if (rank == 2) item.ForeColor = Color.Silver;
                    else if (rank == 3) item.ForeColor = Color.FromArgb(205, 127, 50); // Bronze
                }

                scoreListView.Items.Add(item);
                rank++;
            }
        }

        private void ScoreListView_DrawColumnHeader(object? sender, DrawListViewColumnHeaderEventArgs e)
        {
            using (var sf = new StringFormat())
            {
                sf.Alignment = StringAlignment.Center;
                sf.LineAlignment = StringAlignment.Center;

                using (var headerBrush = new SolidBrush(Color.FromArgb(50, 60, 80)))
                {
                    e.Graphics.FillRectangle(headerBrush, e.Bounds);
                }
                if (e.Header != null && scoreListView != null)
                {
                    e.Graphics.DrawString(e.Header.Text, scoreListView.Font, Brushes.White, e.Bounds, sf);
                }
            }
        }

        private void ScoreListView_DrawSubItem(object? sender, DrawListViewSubItemEventArgs e)
        {
            e.DrawDefault = true;
        }

        private void ScoreListView_DrawItem(object? sender, DrawListViewItemEventArgs e)
        {
            e.DrawDefault = false;
            e.DrawBackground();
            for (int i = 0; i < e.Item.SubItems.Count; i++)
            {
                var subItem = e.Item.SubItems[i];
                if (subItem != null)
                {
                    var bounds = e.Item.GetSubItemAt(e.Item.Position.X + subItem.Bounds.X, e.Item.Position.Y + subItem.Bounds.Y)!.Bounds;
                    TextRenderer.DrawText(e.Graphics, subItem.Text, e.Item.Font, bounds, e.Item.ForeColor, TextFormatFlags.Left | TextFormatFlags.VerticalCenter);
                }
            }
        }
    }
}
