using System;
using System.Collections.Generic;

namespace MillionaireGame.Classes
{
    // This class represents a single question in our game
    // Each question has:
    // - The correct answer
    // - A list of wrong answers (usually 3)
    // - Level and text (from QuestionBase)
    // It also implements IQuestion interface to demonstrate interface usage
    public class Question : QuestionBase, IQuestion
    {
        // Store the correct answer for this question
        public string CorrectAnswer { get; private set; }
        
        // Store a list of wrong answers
        public List<string> WrongAnswers { get; private set; }

        public Question(int level, string text, string correctAnswer, IEnumerable<string> wrongAnswers)
            : base(level, text)
        {
            CorrectAnswer = correctAnswer?.Trim() ?? string.Empty;
            
            WrongAnswers = new List<string>();
            if (wrongAnswers != null)
            {
                foreach (var w in wrongAnswers)
                {
                    var trimmed = w?.Trim();
                    if (!string.IsNullOrEmpty(trimmed))
                        WrongAnswers.Add(trimmed);
                }
            }
        }

        public override bool IsCorrect(string answer)
        {
            return string.Equals(answer?.Trim(), Co<PERSON>ct<PERSON>ns<PERSON>, StringComparison.OrdinalIgnoreCase);
        }

        // Implementation of IQuestion interface
        // This method checks if the user's answer is correct
        public bool CheckAnswer(string userAnswer)
        {
            // Use the existing IsCorrect method
            return IsCorrect(userAnswer);
        }
    }
}

