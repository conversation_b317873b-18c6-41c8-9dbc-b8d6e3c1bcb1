using System;
using System.IO;

namespace MillionaireGame.Classes
{
    // sound manager - handles game sounds
    // uses console beeps for simplicity
    public static class SoundManager
    {
        // Basic settings - simple and straightforward
        private static bool _soundEnabled = true;    // for sound effects
        private static bool _musicEnabled = true;    // for background music
        private static int _volume = 50;             // 50% is a good default

        public static bool SoundEnabled
        {
            get
            {
                return _soundEnabled;
            }
            set
            {
                _soundEnabled = value;
            }
        }

        public static bool MusicEnabled
        {
            get
            {
                return _musicEnabled;
            }
            set
            {
                _musicEnabled = value;
                if (!value)
                {
                    StopBackgroundMusic();
                }
                else
                {
                    PlayBackgroundMusic();
                }
            }
        }

        public static int Volume
        {
            get
            {
                return _volume;
            }
            set
            {
                // Make sure volume is between 0 and 100
                if (value < 0)
                    _volume = 0;
                else if (value > 100)
                    _volume = 100;
                else
                    _volume = value;
            }
        }

        static SoundManager()
        {
            InitializeSounds();
        }

        // Sets up the sound system - simplified version
        private static void InitializeSounds()
        {
            try
            {
                Logger.Log("Sound system initialized (simplified mode - no actual sounds)");
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                Logger.Log("Sound system initialization failed");
            }
        }

        public static void PlayButtonClick()
        {
            if (!_soundEnabled)
                return;

            // Simple beep for button click
            try
            {
                Console.Beep(800, 100);  // Short beep
            }
            catch
            {
                // Ignore if beep fails
            }
        }

        public static void PlayCorrectAnswer()
        {
            if (!_soundEnabled)
                return;

            // Happy beep for correct answer
            try
            {
                Console.Beep(1000, 200);  // Higher pitch, longer duration
            }
            catch
            {
                // Ignore if beep fails
            }
        }

        public static void PlayWrongAnswer()
        {
            if (!_soundEnabled)
                return;

            // Low beep for wrong answer
            try
            {
                Console.Beep(400, 300);  // Lower pitch
            }
            catch
            {
                // Ignore if beep fails
            }
        }

        public static void PlayLifeline()
        {
            if (!_soundEnabled)
                return;

            // Medium beep for lifeline
            try
            {
                Console.Beep(600, 150);
            }
            catch
            {
                // Ignore if beep fails
            }
        }

        public static void PlayWithdraw()
        {
            if (!_soundEnabled)
                return;

            // Beep for withdraw
            try
            {
                Console.Beep(700, 200);
            }
            catch
            {
                // Ignore if beep fails
            }
        }

        public static void PlayMillionaire()
        {
            if (!_soundEnabled)
                return;

            // Victory beeps for millionaire
            try
            {
                Console.Beep(1200, 200);
                Console.Beep(1400, 200);
                Console.Beep(1600, 300);
            }
            catch
            {
                // Ignore if beep fails
            }
        }

        public static void PlayGameOver()
        {
            if (!_soundEnabled)
                return;

            // Sad beep for game over
            try
            {
                Console.Beep(300, 400);
            }
            catch
            {
                // Ignore if beep fails
            }
        }

        public static void PlayTicking()
        {
            if (!_soundEnabled)
                return;

            // Quick beep for ticking
            try
            {
                Console.Beep(500, 50);
            }
            catch
            {
                // Ignore if beep fails
            }
        }

        public static void PlayBackgroundMusic()
        {
            if (!_musicEnabled)
                return;

            // Background music is disabled in simplified mode
            Logger.Log("Background music playback requested (simplified mode - no action)");
        }

        public static void StopBackgroundMusic()
        {
            // Background music is disabled in simplified mode
            Logger.Log("Background music stop requested (simplified mode - no action)");
        }

        public static void Dispose()
        {
            // Nothing to dispose in simplified mode
            Logger.Log("Sound manager disposed");
        }
    }
}
