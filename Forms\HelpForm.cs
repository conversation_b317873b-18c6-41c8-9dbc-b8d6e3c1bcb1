using System;
using System.Drawing;
using System.Windows.Forms;

namespace MillionaireGame.Forms
{
    public partial class HelpForm : Form
    {
        private Label? titleLabel;
        private RichTextBox? helpContentRichTextBox;
        private Button? closeButton;

        public HelpForm()
        {
            InitializeComponent();
            LoadHelpContent();
        }

        private void InitializeComponent()
        {
            this.Text = "Help";
            this.Size = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(20, 30, 50);

            // Title Label
            titleLabel = new Label
            {
                Text = "❓ How to Play ❓",
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 215, 0),
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.Transparent
            };

            // Help Content RichTextBox
            helpContentRichTextBox = new RichTextBox
            {
                Location = new Point(30, 100),
                Size = new Size(640, 400),
                BackColor = Color.FromArgb(45, 45, 48),
                ForeColor = Color.Gainsboro,
                ReadOnly = true,
                BorderStyle = BorderStyle.None,
                Font = new Font("Segoe UI", 12)
            };

            // Close Button
            closeButton = new Button
            {
                Text = "Got It!",
                Location = new Point(290, 520),
                Size = new Size(120, 40),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.FromArgb(0, 123, 255),
                Cursor = Cursors.Hand
            };
            closeButton.FlatAppearance.BorderSize = 0;
            closeButton.Click += CloseButton_Click;

            this.Controls.Add(titleLabel);
            this.Controls.Add(helpContentRichTextBox);
            this.Controls.Add(closeButton);

            LoadHelpContent();
        }

        private void LoadHelpContent()
        {
            // safety check first
            if (helpContentRichTextBox == null) return;

            // start fresh
            helpContentRichTextBox.Clear();

            // Introduction
            AppendText("Welcome to 'Who Wants to Be a Millionaire'!\n\n", new Font("Segoe UI", 16, FontStyle.Bold), Color.Gold);

            // Rules
            AppendText("Rules of the Game:\n", new Font("Segoe UI", 14, FontStyle.Bold), Color.White);
            AppendText("1.  You will be asked 15 multiple-choice questions.\n", Color.Gainsboro);
            AppendText("2.  Each question has four possible answers, but only one is correct.\n", Color.Gainsboro);
            AppendText("3.  For each correct answer, you win a certain amount of money.\n", Color.Gainsboro);
            AppendText("4.  The prize money increases with each question.\n", Color.Gainsboro);
            AppendText("5.  There are two 'safe havens' at question 5 ($1,000) and question 10 ($32,000). If you reach these points, you are guaranteed to leave with at least that amount.\n", Color.Gainsboro);
            AppendText("6.  If you answer incorrectly, the game ends, and you win the amount from the last safe haven you passed.\n\n", Color.Gainsboro);

            // Lifelines
            AppendText("Lifelines:\n", new Font("Segoe UI", 14, FontStyle.Bold), Color.White);
            AppendText("You have three lifelines to help you during the game. Each can be used only once:\n", Color.Gainsboro);
            AppendText("   •  50:50: The computer eliminates two incorrect answers, leaving one incorrect and the correct answer.\n", Color.LightCyan);
            AppendText("   •  Phone a Friend: You can 'call' a friend for help (a hint will be provided).\n", Color.LightCyan);
            AppendText("   •  Ask the Audience: The studio audience 'votes' on the answers, showing you the probabilities.\n\n", Color.LightCyan);

            // Winning
            AppendText("Winning the Game:\n", new Font("Segoe UI", 14, FontStyle.Bold), Color.White);
            AppendText("Answer all 15 questions correctly to win the grand prize of $1,000,000!\n\n", Color.Gainsboro);

            // Walking Away
            AppendText("Walking Away:\n", new Font("Segoe UI", 14, FontStyle.Bold), Color.White);
            AppendText("At any point, before confirming an answer, you can choose to 'Withdraw' and walk away with the money you have won so far.\n", Color.Gainsboro);
        }

        // Helper method to add colored text
        // This was tricky to figure out but works great!
        // Note: need to set selection before changing color
        private void AppendText(string text, Color color)
        {
            if (helpContentRichTextBox == null) return;  // always check for null
            
            // move to end of text and select nothing
            helpContentRichTextBox.SelectionStart = helpContentRichTextBox.TextLength;
            helpContentRichTextBox.SelectionLength = 0;  // zero = no selection
            helpContentRichTextBox.SelectionColor = color;
            helpContentRichTextBox.AppendText(text);
        }

        private void AppendText(string text, Font font, Color color)
        {
            if (helpContentRichTextBox == null) return;
            helpContentRichTextBox.SelectionStart = helpContentRichTextBox.TextLength;
            helpContentRichTextBox.SelectionLength = 0;
            helpContentRichTextBox.SelectionFont = font;
            helpContentRichTextBox.SelectionColor = color;
            helpContentRichTextBox.AppendText(text);
        }

        private void CloseButton_Click(object? sender, EventArgs e)
        {
            this.Close();
        }
    }
}
