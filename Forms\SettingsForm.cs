using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MillionaireGame.Classes;

namespace MillionaireGame
{
    public class SettingsForm : Form
    {
        private Panel? topPanel;
        private Label? lblTitle;
        private GroupBox? grpDifficulty;
        private RadioButton? rdoEasy;
        private RadioButton? rdoMedium;
        private RadioButton? rdoHard;
        private GroupBox? grpSound;
        private CheckBox? chkSoundEffects;
        private CheckBox? chkBackgroundMusic;
        private TrackBar? trkVolume;
        private Label? lblVolume;
        private GroupBox? grpLanguage;
        private RadioButton? rdoArabic;
        private RadioButton? rdoEnglish;
        private GroupBox? grpTimer;
        private CheckBox? chkEnableTimer;
        private NumericUpDown? nudTimerSeconds;
        private Label? lblTimerSeconds;
        private GroupBox? grpDisplay;
        private CheckBox? chkFullscreen;
        private ComboBox? cmbTheme;
        private Label? lblTheme;
        private Button? btnSave;
        private Button? btnCancel;
        private Button? btnReset;

        public SettingsForm()
        {
            InitializeComponents();
            LoadSettings();
        }

        private void InitializeComponents()
        {
            this.Text = "Settings";
            this.Size = new Size(600, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(24, 33, 58);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Top panel
            topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(34, 47, 86)
            };

            lblTitle = new Label
            {
                Text = "⚙️ GAME SETTINGS",
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 215, 0),
                Dock = DockStyle.Fill
            };

            // Difficulty settings
            grpDifficulty = CreateGroupBox("🎯 Difficulty Level", 80, 120);
            rdoEasy = CreateRadioButton("Easy (More time, easier questions)", 20, 25);
            rdoMedium = CreateRadioButton("Medium (Standard gameplay)", 20, 50);
            rdoHard = CreateRadioButton("Hard (Less time, harder questions)", 20, 75);

            // Sound settings
            grpSound = CreateGroupBox("🔊 Sound Settings", 220, 150);
            chkSoundEffects = CreateCheckBox("Enable sound effects", 20, 25);
            chkBackgroundMusic = CreateCheckBox("Enable background music", 20, 50);
            
            lblVolume = new Label
            {
                Text = "Volume: 50%",
                Location = new Point(20, 80),
                Size = new Size(100, 20),
                ForeColor = Color.White,
                Font = new Font("Arial", 10)
            };

            trkVolume = new TrackBar
            {
                Location = new Point(20, 100),
                Size = new Size(200, 45),
                Minimum = 0,
                Maximum = 100,
                Value = 50,
                TickFrequency = 10
            };
            trkVolume.ValueChanged += TrkVolume_ValueChanged;

            // Language settings
            grpLanguage = CreateGroupBox("🌍 Language", 390, 80);
            rdoArabic = CreateRadioButton("العربية", 20, 25);
            rdoEnglish = CreateRadioButton("English", 20, 50);

            // Timer settings
            grpTimer = CreateGroupBox("⏱️ Timer Settings", 490, 100);
            chkEnableTimer = CreateCheckBox("Enable question timer", 20, 25);
            
            lblTimerSeconds = new Label
            {
                Text = "Seconds per question:",
                Location = new Point(20, 55),
                Size = new Size(150, 20),
                ForeColor = Color.White,
                Font = new Font("Arial", 10)
            };

            nudTimerSeconds = new NumericUpDown
            {
                Location = new Point(180, 53),
                Size = new Size(60, 25),
                Minimum = 10,
                Maximum = 120,
                Value = 30,
                Font = new Font("Arial", 10)
            };

            // Display settings
            grpDisplay = CreateGroupBox("🖥️ Display Settings", 610, 100);
            chkFullscreen = CreateCheckBox("Fullscreen mode", 20, 25);
            
            lblTheme = new Label
            {
                Text = "Theme:",
                Location = new Point(20, 55),
                Size = new Size(60, 20),
                ForeColor = Color.White,
                Font = new Font("Arial", 10)
            };

            cmbTheme = new ComboBox
            {
                Location = new Point(90, 53),
                Size = new Size(120, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Arial", 10)
            };
            cmbTheme.Items.AddRange(new[] { "Dark Blue", "Classic", "Modern", "High Contrast" });
            cmbTheme.SelectedIndex = 0;

            // Action buttons
            btnSave = new Button
            {
                Text = "💾 Save Settings",
                Location = new Point(50, 620),
                Size = new Size(150, 40),
                Font = new Font("Arial", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSave.Click += BtnSave_Click;

            btnReset = new Button
            {
                Text = "🔄 Reset to Default",
                Location = new Point(220, 620),
                Size = new Size(160, 40),
                Font = new Font("Arial", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            btnReset.Click += BtnReset_Click;

            btnCancel = new Button
            {
                Text = "❌ Cancel",
                Location = new Point(400, 620),
                Size = new Size(120, 40),
                Font = new Font("Arial", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnCancel.Click += BtnCancel_Click;

            // add controls to groups
            if (grpDifficulty != null) grpDifficulty.Controls.AddRange(new Control[] { rdoEasy!, rdoMedium!, rdoHard! });
            if (grpSound != null) grpSound.Controls.AddRange(new Control[] { chkSoundEffects!, chkBackgroundMusic!, lblVolume!, trkVolume! });
            if (grpLanguage != null) grpLanguage.Controls.AddRange(new Control[] { rdoArabic!, rdoEnglish! });
            // timer group
            if (grpTimer != null) grpTimer.Controls.AddRange(new Control[] { chkEnableTimer!, lblTimerSeconds!, nudTimerSeconds! });
            if (grpDisplay != null) grpDisplay.Controls.AddRange(new Control[] { chkFullscreen!, lblTheme!, cmbTheme! });

            if (topPanel != null) topPanel.Controls.Add(lblTitle);

            // Add all controls to form
            var controlsToAdd = new List<Control>();
            if (topPanel != null) controlsToAdd.Add(topPanel);
            if (grpDifficulty != null) controlsToAdd.Add(grpDifficulty);
            if (grpSound != null) controlsToAdd.Add(grpSound);
            if (grpLanguage != null) controlsToAdd.Add(grpLanguage);
            if (grpTimer != null) controlsToAdd.Add(grpTimer);
            if (grpDisplay != null) controlsToAdd.Add(grpDisplay);
            if (btnSave != null) controlsToAdd.Add(btnSave);
            if (btnReset != null) controlsToAdd.Add(btnReset);
            if (btnCancel != null) controlsToAdd.Add(btnCancel);
            this.Controls.AddRange(controlsToAdd.ToArray());
        }

        // Helper method to create group boxes - saves typing the same stuff over and over
        // Found this pattern in a C# best practices article 👍
        private GroupBox? CreateGroupBox(string text, int y, int height)
        {
            return new GroupBox
            {
                Text = text,
                Location = new Point(20, y),
                Size = new Size(540, height),
                ForeColor = Color.FromArgb(255, 215, 0),
                Font = new Font("Arial", 12, FontStyle.Bold)
            };
        }

        // Another helper for radio buttons - DRY principle in action! 
        private RadioButton? CreateRadioButton(string text, int x, int y)
        {
            return new RadioButton
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(300, 20),
                ForeColor = Color.White,
                Font = new Font("Arial", 10)
            };
        }

        // And one more for checkboxes - really helps keep code clean and consistent
        private CheckBox? CreateCheckBox(string text, int x, int y)
        {
            return new CheckBox
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(200, 20),
                ForeColor = Color.White,
                Font = new Font("Arial", 10)
            };
        }

        // Updates the volume label as the user moves the slider - simple but helpful!
        private void TrkVolume_ValueChanged(object? sender, EventArgs e)
        {
            if (lblVolume != null && trkVolume != null)
            {
                lblVolume.Text = $"Volume: {trkVolume.Value}%";
            }
        }

        // Loads all settings from the GameSettings singleton
        // Singleton pattern is pretty cool - learned about it in class! 🎓
        private void LoadSettings()
        {
            var settings = GameSettings.Instance;

            // Load difficulty settings
            if (rdoEasy != null && rdoMedium != null && rdoHard != null)
            {
                switch (settings.Difficulty)
                {
                    case GameSettings.DifficultyLevel.Easy:
                        rdoEasy.Checked = true;
                        break;
                    case GameSettings.DifficultyLevel.Medium:
                        rdoMedium.Checked = true;
                        break;
                    case GameSettings.DifficultyLevel.Hard:
                        rdoHard.Checked = true;
                        break;
                }
            }

            // Load sound settings
            if (chkSoundEffects != null) chkSoundEffects.Checked = settings.SoundEffectsEnabled;
            if (chkBackgroundMusic != null) chkBackgroundMusic.Checked = settings.BackgroundMusicEnabled;
            if (trkVolume != null) trkVolume.Value = settings.Volume;
            if (lblVolume != null) lblVolume.Text = $"Volume: {settings.Volume}%";

            // Load language settings
            if (rdoArabic != null && rdoEnglish != null)
            {
                switch (settings.CurrentLanguage)
                {
                    case GameSettings.Language.Arabic:
                        rdoArabic.Checked = true;
                        break;
                    case GameSettings.Language.English:
                        rdoEnglish.Checked = true;
                        break;
                }
            }

            // Load timer settings
            if (chkEnableTimer != null) chkEnableTimer.Checked = settings.TimerEnabled;
            if (nudTimerSeconds != null) nudTimerSeconds.Value = settings.TimerSeconds;

            // Load display settings
            if (chkFullscreen != null) chkFullscreen.Checked = settings.FullscreenMode;
            if (cmbTheme != null) cmbTheme.SelectedIndex = (int)settings.CurrentTheme;
        }

        // Saves all settings when the user clicks Save
        // Wrapped everything in try-catch because file operations can be tricky 😅
        private void BtnSave_Click(object? sender, EventArgs e)
        {
            try
            {
                var settings = GameSettings.Instance;

                // Save difficulty settings
                if (rdoEasy != null && rdoEasy.Checked)
                    settings.Difficulty = GameSettings.DifficultyLevel.Easy;
                else if (rdoMedium != null && rdoMedium.Checked)
                    settings.Difficulty = GameSettings.DifficultyLevel.Medium;
                else if (rdoHard != null && rdoHard.Checked)
                    settings.Difficulty = GameSettings.DifficultyLevel.Hard;

                // Save sound settings
                if (chkSoundEffects != null) settings.SoundEffectsEnabled = chkSoundEffects.Checked;
                if (chkBackgroundMusic != null) settings.BackgroundMusicEnabled = chkBackgroundMusic.Checked;
                if (trkVolume != null) settings.Volume = trkVolume.Value;

                // Save language settings
                if (rdoArabic != null && rdoArabic.Checked)
                    settings.CurrentLanguage = GameSettings.Language.Arabic;
                else if (rdoEnglish != null && rdoEnglish.Checked)
                    settings.CurrentLanguage = GameSettings.Language.English;

                // Save timer settings
                if (chkEnableTimer != null) settings.TimerEnabled = chkEnableTimer.Checked;
                if (nudTimerSeconds != null) settings.TimerSeconds = (int)nudTimerSeconds.Value;

                // Save display settings
                if (chkFullscreen != null) settings.FullscreenMode = chkFullscreen.Checked;
                if (cmbTheme != null) settings.CurrentTheme = (GameSettings.Theme)cmbTheme.SelectedIndex;

                // Apply and save settings
                settings.ApplySettings();
                bool saved = settings.SaveSettings();

                if (saved)
                {
                    MessageBox.Show("Settings saved successfully!", "Settings",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Failed to save settings. Please try again.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                MessageBox.Show("An error occurred while saving settings.", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Asks for confirmation before resetting - don't want accidental resets! 🤔
        private void BtnReset_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("Reset all settings to default values?", "Reset Settings",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                LoadSettings(); // Reset to defaults
            }
        }

        // Just close the form - changes aren't saved unless Save is clicked
        private void BtnCancel_Click(object? sender, EventArgs e)
        {
            this.Close();
        }
    }
}
