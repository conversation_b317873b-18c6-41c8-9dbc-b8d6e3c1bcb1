using System;
using System.IO;

namespace MillionaireGame.Classes
{
    public class GameSettings
    {
        // difficulty levels
        public enum DifficultyLevel
        {
            Easy,
            Medium,
            Hard
        }

        public enum Language
        {
            English,
            Arabic
        }

        public enum Theme
        {
            DarkBlue,
            Classic,
            Modern,
            HighContrast
        }

        // game settings properties
        public DifficultyLevel Difficulty { get; set; } = DifficultyLevel.Medium;
        public Language CurrentLanguage { get; set; } = Language.English;
        public Theme CurrentTheme { get; set; } = Theme.DarkBlue;

        public bool SoundEffectsEnabled { get; set; } = true;
        public bool BackgroundMusicEnabled { get; set; } = true;
        public int Volume { get; set; } = 50;

        public bool TimerEnabled { get; set; } = true;
        public int TimerSeconds { get; set; } = 30;

        public bool FullscreenMode { get; set; } = false;

        public bool ShowHints { get; set; } = true;
        public bool AutoSaveProgress { get; set; } = true;
        public bool ShowStatistics { get; set; } = true;

        private static readonly string SettingsFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "settings.txt");

        private static GameSettings? _instance;

        public static GameSettings Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = LoadSettings();
                }
                return _instance;
            }
        }

        private static GameSettings LoadSettings()
        {
            try
            {
                if (File.Exists(SettingsFilePath))
                {
                    GameSettings settings = new GameSettings();

                    using (StreamReader sr = new StreamReader(SettingsFilePath))
                    {
                        string line;
                        while ((line = sr.ReadLine()) != null)
                        {
                            if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                                continue;

                            string[] parts = line.Split('=');
                            if (parts.Length != 2)
                                continue;

                            string key = parts[0].Trim();
                            string value = parts[1].Trim();

                            if (key == "Difficulty")
                            {
                                if (value == "Easy")
                                    settings.Difficulty = DifficultyLevel.Easy;
                                else if (value == "Medium")
                                    settings.Difficulty = DifficultyLevel.Medium;
                                else if (value == "Hard")
                                    settings.Difficulty = DifficultyLevel.Hard;
                            }
                            else if (key == "CurrentLanguage")
                            {
                                if (value == "English")
                                    settings.CurrentLanguage = Language.English;
                                else if (value == "Arabic")
                                    settings.CurrentLanguage = Language.Arabic;
                            }
                            else if (key == "CurrentTheme")
                            {
                                if (value == "DarkBlue")
                                    settings.CurrentTheme = Theme.DarkBlue;
                                else if (value == "Classic")
                                    settings.CurrentTheme = Theme.Classic;
                                else if (value == "Modern")
                                    settings.CurrentTheme = Theme.Modern;
                                else if (value == "HighContrast")
                                    settings.CurrentTheme = Theme.HighContrast;
                            }
                            else if (key == "SoundEffectsEnabled")
                            {
                                settings.SoundEffectsEnabled = value == "True";
                            }
                            else if (key == "BackgroundMusicEnabled")
                            {
                                settings.BackgroundMusicEnabled = value == "True";
                            }
                            else if (key == "Volume")
                            {
                                int volume;
                                if (int.TryParse(value, out volume))
                                    settings.Volume = volume;
                            }
                            else if (key == "TimerEnabled")
                            {
                                settings.TimerEnabled = value == "True";
                            }
                            else if (key == "TimerSeconds")
                            {
                                int seconds;
                                if (int.TryParse(value, out seconds))
                                    settings.TimerSeconds = seconds;
                            }
                            else if (key == "FullscreenMode")
                            {
                                settings.FullscreenMode = value == "True";
                            }
                            else if (key == "ShowHints")
                            {
                                settings.ShowHints = value == "True";
                            }
                            else if (key == "AutoSaveProgress")
                            {
                                settings.AutoSaveProgress = value == "True";
                            }
                            else if (key == "ShowStatistics")
                            {
                                settings.ShowStatistics = value == "True";
                            }
                        }
                    }

                    Logger.Log("Game settings loaded successfully");
                    return settings;
                }
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                Logger.Log("Failed to load settings, using defaults");
            }

            Logger.Log("Using default game settings");
            return new GameSettings();
        }

        // save settings to file
        public bool SaveSettings()
        {
            try
            {
                // Write settings to text file in simple key=value format
                using (StreamWriter sw = new StreamWriter(SettingsFilePath))
                {
                    sw.WriteLine("# Game Settings File");
                    sw.WriteLine("# This file stores all game configuration");
                    sw.WriteLine();

                    // Write each setting on a separate line
                    sw.WriteLine("Difficulty=" + Difficulty.ToString());
                    sw.WriteLine("CurrentLanguage=" + CurrentLanguage.ToString());
                    sw.WriteLine("CurrentTheme=" + CurrentTheme.ToString());
                    sw.WriteLine("SoundEffectsEnabled=" + SoundEffectsEnabled.ToString());
                    sw.WriteLine("BackgroundMusicEnabled=" + BackgroundMusicEnabled.ToString());
                    sw.WriteLine("Volume=" + Volume.ToString());
                    sw.WriteLine("TimerEnabled=" + TimerEnabled.ToString());
                    sw.WriteLine("TimerSeconds=" + TimerSeconds.ToString());
                    sw.WriteLine("FullscreenMode=" + FullscreenMode.ToString());
                    sw.WriteLine("ShowHints=" + ShowHints.ToString());
                    sw.WriteLine("AutoSaveProgress=" + AutoSaveProgress.ToString());
                    sw.WriteLine("ShowStatistics=" + ShowStatistics.ToString());
                }

                Logger.Log("Game settings saved successfully");
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                Logger.Log("Failed to save game settings");
                return false;
            }
        }

        public void ApplySettings()
        {
            try
            {
                // Apply sound settings
                SoundManager.SoundEnabled = SoundEffectsEnabled;
                SoundManager.MusicEnabled = BackgroundMusicEnabled;
                SoundManager.Volume = Volume;

                // Apply other settings as needed
                Logger.Log("Game settings applied successfully");
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                Logger.Log("Failed to apply some game settings");
            }
        }

        // Reset everything back to normal
        // Sometimes users mess up settings, this helps fix that!
        public void ResetToDefaults()
        {
            // Back to medium - not too hard, not too easy
            Difficulty = DifficultyLevel.Medium;
            
            // English is default - might change based on user's system later
            CurrentLanguage = Language.English;
            
            // Dark blue theme looks best IMO
            CurrentTheme = Theme.DarkBlue;
            
            // Turn all the fun stuff back on
            SoundEffectsEnabled = true;     // beeps and boops!
            BackgroundMusicEnabled = true;   // adds atmosphere
            Volume = 50;                     // not too loud
            
            // Timer settings - 30 sec seems fair
            TimerEnabled = true;
            TimerSeconds = 30;
            
            // Other stuff
            FullscreenMode = false;          // windowed mode is safer
            ShowHints = true;                // everyone needs help sometimes
            AutoSaveProgress = true;         // don't want to lose progress
            ShowStatistics = true;           // stats are fun!

            Logger.Log("Game settings reset to defaults");
        }

        public int GetTimerSecondsForDifficulty()
        {
            if (!TimerEnabled)
                return 0;

            // Calculate timer based on difficulty level
            if (Difficulty == DifficultyLevel.Easy)
            {
                return TimerSeconds + 15;  // Extra 15 seconds for easy
            }
            else if (Difficulty == DifficultyLevel.Medium)
            {
                return TimerSeconds;  // Standard time
            }
            else if (Difficulty == DifficultyLevel.Hard)
            {
                // 10 seconds less for hard, minimum 10
                int hardTime = TimerSeconds - 10;
                if (hardTime < 10)
                    return 10;
                else
                    return hardTime;
            }
            else
            {
                return TimerSeconds;  // Default
            }
        }

        public string GetDifficultyDisplayName()
        {
            // Return difficulty name based on current language
            if (Difficulty == DifficultyLevel.Easy)
            {
                if (CurrentLanguage == Language.Arabic)
                    return "سهل";
                else
                    return "Easy";
            }
            else if (Difficulty == DifficultyLevel.Medium)
            {
                if (CurrentLanguage == Language.Arabic)
                    return "متوسط";
                else
                    return "Medium";
            }
            else if (Difficulty == DifficultyLevel.Hard)
            {
                if (CurrentLanguage == Language.Arabic)
                    return "صعب";
                else
                    return "Hard";
            }
            else
            {
                return "Medium";  // Default
            }
        }

        public string GetLanguageDisplayName()
        {
            // Return language name in its native form
            if (CurrentLanguage == Language.Arabic)
            {
                return "العربية";
            }
            else if (CurrentLanguage == Language.English)
            {
                return "English";
            }
            else
            {
                return "English";  // Default
            }
        }

        public string GetThemeDisplayName()
        {
            // Return theme name based on current language
            if (CurrentTheme == Theme.DarkBlue)
            {
                if (CurrentLanguage == Language.Arabic)
                    return "أزرق داكن";
                else
                    return "Dark Blue";
            }
            else if (CurrentTheme == Theme.Classic)
            {
                if (CurrentLanguage == Language.Arabic)
                    return "كلاسيكي";
                else
                    return "Classic";
            }
            else if (CurrentTheme == Theme.Modern)
            {
                if (CurrentLanguage == Language.Arabic)
                    return "حديث";
                else
                    return "Modern";
            }
            else if (CurrentTheme == Theme.HighContrast)
            {
                if (CurrentLanguage == Language.Arabic)
                    return "تباين عالي";
                else
                    return "High Contrast";
            }
            else
            {
                return "Dark Blue";  // Default
            }
        }

        // Localization helper methods
        public string GetLocalizedText(string englishText, string arabicText)
        {
            // Return text based on current language
            if (CurrentLanguage == Language.Arabic)
                return arabicText;
            else
                return englishText;
        }

        public string GetLocalizedText(string key)
        {
            // This could be expanded to use a proper localization system
            // Using if-else statements to check each key
            if (key == "NewGame")
            {
                return GetLocalizedText("New Game", "لعبة جديدة");
            }
            else if (key == "HighScores")
            {
                return GetLocalizedText("High Scores", "أفضل النتائج");
            }
            else if (key == "Settings")
            {
                return GetLocalizedText("Settings", "الإعدادات");
            }
            else if (key == "Statistics")
            {
                return GetLocalizedText("Statistics", "الإحصائيات");
            }
            else if (key == "Help")
            {
                return GetLocalizedText("Help", "المساعدة");
            }
            else if (key == "Exit")
            {
                return GetLocalizedText("Exit", "خروج");
            }
            else if (key == "Level")
            {
                return GetLocalizedText("Level", "المستوى");
            }
            else if (key == "Question")
            {
                return GetLocalizedText("Question", "السؤال");
            }
            else if (key == "CorrectAnswer")
            {
                return GetLocalizedText("Correct Answer!", "إجابة صحيحة!");
            }
            else if (key == "WrongAnswer")
            {
                return GetLocalizedText("Wrong Answer!", "إجابة خاطئة!");
            }
            else if (key == "GameOver")
            {
                return GetLocalizedText("Game Over", "انتهت اللعبة");
            }
            else if (key == "Congratulations")
            {
                return GetLocalizedText("Congratulations!", "مبروك!");
            }
            else if (key == "YouWon")
            {
                return GetLocalizedText("You Won", "لقد فزت");
            }
            else if (key == "FinalAmount")
            {
                return GetLocalizedText("Final Amount", "المبلغ النهائي");
            }
            else
            {
                return key;  // Return the key itself if not found
            }
        }
    }
}
