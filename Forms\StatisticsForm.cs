using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using MillionaireGame.Classes;

namespace MillionaireGame
{
    public class StatisticsForm : Form
    {
        private Panel? topPanel;
        private Label? lblTitle;
        private GroupBox? grpOverallStats;
        private Label? lblTotalGames;
        private Label? lblTotalWinnings;
        private Label? lblAverageScore;
        private Label? lblBestScore;
        private Label? lblSuccessRate;
        private GroupBox? grpLevelStats;
        private ListView? listLevelStats;
        private GroupBox? grpTimeStats;
        private Label? lblAverageTime;
        private Label? lblBestTime;
        private Label? lblTotalPlayTime;
        private GroupBox? grpAchievements;
        private FlowLayoutPanel? pnlAchievements;
        private Button? btnClose;
        private Button? btnReset;
        private ProgressBar? progressOverall;
        private Label? lblProgress;

        public StatisticsForm()
        {
            InitializeComponent();
            LoadStatistics();
        }

        private void InitializeComponent()
        {
            this.Text = "Player Statistics";
            this.Size = new Size(800, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(24, 33, 58);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Top panel
            topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(34, 47, 86)
            };

            lblTitle = new Label
            {
                Text = "📊 PLAYER STATISTICS",
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Arial", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 215, 0),
                Dock = DockStyle.Fill
            };

            // Overall Statistics
            grpOverallStats = CreateGroupBox("🎯 Overall Performance", 80, 180);

            lblTotalGames = CreateStatLabel("Total Games Played: 0", 20, 25);
            lblTotalWinnings = CreateStatLabel("Total Winnings: $0", 20, 50);
            lblAverageScore = CreateStatLabel("Average Score: $0", 20, 75);
            lblBestScore = CreateStatLabel("Best Score: $0", 20, 100);
            lblSuccessRate = CreateStatLabel("Success Rate: 0%", 20, 125);

            progressOverall = new ProgressBar
            {
                Location = new Point(300, 25),
                Size = new Size(200, 20),
                Style = ProgressBarStyle.Continuous
            };

            lblProgress = new Label
            {
                Text = "Overall Progress",
                Location = new Point(300, 50),
                Size = new Size(200, 20),
                ForeColor = Color.White,
                Font = new Font("Arial", 10),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // Level Statistics
            grpLevelStats = CreateGroupBox("📈 Level Performance", 280, 200);

            listLevelStats = new ListView
            {
                Location = new Point(20, 25),
                Size = new Size(500, 150),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                BackColor = Color.FromArgb(45, 45, 48),
                ForeColor = Color.White,
                Font = new Font("Arial", 9)
            };

            listLevelStats.Columns.Add("Level", 60, HorizontalAlignment.Center);
            listLevelStats.Columns.Add("Times Reached", 100, HorizontalAlignment.Center);
            listLevelStats.Columns.Add("Success Rate", 100, HorizontalAlignment.Center);
            listLevelStats.Columns.Add("Avg Time", 80, HorizontalAlignment.Center);
            listLevelStats.Columns.Add("Best Time", 80, HorizontalAlignment.Center);
            listLevelStats.Columns.Add("Prize Value", 80, HorizontalAlignment.Right);

            // Time Statistics
            grpTimeStats = CreateGroupBox("⏱️ Time Statistics", 500, 120);

            lblAverageTime = CreateStatLabel("Average Game Time: 00:00", 20, 25);
            lblBestTime = CreateStatLabel("Best Game Time: 00:00", 20, 50);
            lblTotalPlayTime = CreateStatLabel("Total Play Time: 00:00", 20, 75);

            // Achievements
            grpAchievements = CreateGroupBox("🏆 Achievements", 640, 120);

            pnlAchievements = new FlowLayoutPanel
            {
                Location = new Point(20, 25),
                Size = new Size(500, 80),
                FlowDirection = FlowDirection.LeftToRight,
                WrapContents = true,
                BackColor = Color.Transparent
            };

            // Action buttons
            btnReset = new Button
            {
                Text = "🔄 Reset Statistics",
                Location = new Point(50, 620),
                Size = new Size(160, 40),
                Font = new Font("Arial", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };
            btnReset.Click += BtnReset_Click;

            btnClose = new Button
            {
                Text = "❌ Close",
                Location = new Point(580, 620),
                Size = new Size(120, 40),
                Font = new Font("Arial", 12, FontStyle.Bold),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnClose.Click += BtnClose_Click;

            // Add controls to groups
            if (grpOverallStats != null)
            {
                grpOverallStats.Controls.AddRange(new Control[] {
                    lblTotalGames!, lblTotalWinnings!, lblAverageScore!, lblBestScore!, lblSuccessRate!,
                    progressOverall!, lblProgress!
                });
            }
            if (grpLevelStats != null && listLevelStats != null) grpLevelStats.Controls.Add(listLevelStats);
            if (grpTimeStats != null) grpTimeStats.Controls.AddRange(new Control[] { lblAverageTime!, lblBestTime!, lblTotalPlayTime! });
            if (grpAchievements != null && pnlAchievements != null) grpAchievements.Controls.Add(pnlAchievements);

            if (topPanel != null && lblTitle != null) topPanel.Controls.Add(lblTitle);

            // Add all controls to form
            this.Controls.AddRange(new Control[] {
                topPanel!, grpOverallStats!, grpLevelStats!, grpTimeStats!, grpAchievements!,
                btnReset, btnClose
            });
        }

        private GroupBox CreateGroupBox(string text, int y, int height)
        {
            return new GroupBox
            {
                Text = text,
                Location = new Point(20, y),
                Size = new Size(740, height),
                ForeColor = Color.FromArgb(255, 215, 0),
                Font = new Font("Arial", 12, FontStyle.Bold)
            };
        }

        private Label CreateStatLabel(string text, int x, int y)
        {
            return new Label
            {
                Text = text,
                Location = new Point(x, y),
                Size = new Size(250, 20),
                ForeColor = Color.White,
                Font = new Font("Arial", 11, FontStyle.Bold)
            };
        }

        private void LoadStatistics()
        {
            try
            {
                var results = GameResultsManager.LoadResults();

                if (results.Count == 0)
                {
                    ShowEmptyStats();
                    return;
                }

                // Calculate overall statistics
                int totalGames = results.Count;

                long totalWinnings = 0;
                long totalScore = 0;
                int bestScore = 0;
                int millionaires = 0;

                foreach (var result in results)
                {
                    totalWinnings += result.AmountWon;
                    totalScore += result.AmountWon;
                    if (result.AmountWon > bestScore)
                        bestScore = result.AmountWon;
                    if (result.AmountWon >= 1000000)
                        millionaires++;
                }

                double averageScore = totalGames > 0 ? (double)totalScore / totalGames : 0;
                double successRate = totalGames > 0 ? (double)millionaires / totalGames * 100 : 0;

                if (lblTotalGames != null) lblTotalGames.Text = $"Total Games Played: {totalGames:N0}";
                if (lblTotalWinnings != null) lblTotalWinnings.Text = $"Total Winnings: ${totalWinnings:N0}";
                if (lblAverageScore != null) lblAverageScore.Text = $"Average Score: ${averageScore:N0}";
                if (lblBestScore != null) lblBestScore.Text = $"Best Score: ${bestScore:N0}";
                if (lblSuccessRate != null) lblSuccessRate.Text = $"Success Rate: {successRate:F1}%";

                if (progressOverall != null) progressOverall.Value = Math.Min(100, (int)(successRate));

                // Calculate time statistics
                long totalTicks = 0;
                TimeSpan bestTime = TimeSpan.MaxValue;
                bool hasBestTime = false;

                foreach (var result in results)
                {
                    totalTicks += result.GameDuration.Ticks;
                    if (result.AmountWon > 0 && result.GameDuration < bestTime)
                    {
                        bestTime = result.GameDuration;
                        hasBestTime = true;
                    }
                }

                var avgTime = totalGames > 0 ? TimeSpan.FromTicks(totalTicks / totalGames) : TimeSpan.Zero;
                if (!hasBestTime) bestTime = TimeSpan.Zero;
                var totalTime = TimeSpan.FromTicks(totalTicks);

                if (lblAverageTime != null) lblAverageTime.Text = $"Average Game Time: {avgTime:mm\\:ss}";
                if (lblBestTime != null) lblBestTime.Text = $"Best Game Time: {bestTime:mm\\:ss}";
                if (lblTotalPlayTime != null) lblTotalPlayTime.Text = $"Total Play Time: {totalTime:hh\\:mm\\:ss}";

                // Load level statistics
                LoadLevelStatistics(results);

                // Load achievements
                LoadAchievements(results);
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                ShowEmptyStats();
            }
        }

        private void LoadLevelStatistics(List<GameResult> results)
        {
            if (listLevelStats == null) return;
            listLevelStats.Items.Clear();

            var prizeValues = new[] { "100", "200", "300", "500", "1000", "2000", "4000", "8000",
                                    "16000", "32000", "64000", "125000", "250000", "500000", "1000000" };

            for (int level = 1; level <= 15; level++)
            {
                // Count results that reached this level
                var levelResults = new List<GameResult>();
                foreach (var result in results)
                {
                    if (result.LevelReached >= level)
                        levelResults.Add(result);
                }

                int timesReached = levelResults.Count;
                if (timesReached == 0) continue;

                // Count results that completed this level (reached next level)
                int levelCompletions = 0;
                foreach (var result in results)
                {
                    if (result.LevelReached > level)
                        levelCompletions++;
                }

                double successRate = timesReached > 0 ? (double)levelCompletions / timesReached * 100 : 0;

                // Calculate average and best time for this level
                long totalTicks = 0;
                TimeSpan bestTime = TimeSpan.MaxValue;
                bool hasBestTime = false;

                foreach (var result in levelResults)
                {
                    totalTicks += result.GameDuration.Ticks;
                    if (result.GameDuration < bestTime)
                    {
                        bestTime = result.GameDuration;
                        hasBestTime = true;
                    }
                }

                var avgTime = levelResults.Count > 0 ? TimeSpan.FromTicks(totalTicks / levelResults.Count) : TimeSpan.Zero;
                if (!hasBestTime) bestTime = TimeSpan.Zero;

                var item = new ListViewItem(level.ToString());
                item.SubItems.Add(timesReached.ToString());
                item.SubItems.Add($"{successRate:F1}%");
                item.SubItems.Add(avgTime.ToString(@"mm\:ss"));
                item.SubItems.Add(bestTime.ToString(@"mm\:ss"));
                item.SubItems.Add($"${prizeValues[level - 1]}");

                // Color coding
                if (successRate >= 80) item.BackColor = Color.FromArgb(76, 175, 80);
                else if (successRate >= 60) item.BackColor = Color.FromArgb(255, 193, 7);
                else if (successRate >= 40) item.BackColor = Color.FromArgb(255, 152, 0);
                else item.BackColor = Color.FromArgb(244, 67, 54);

                listLevelStats.Items.Add(item);
            }
        }

        private void LoadAchievements(List<GameResult> results)
        {
            if (pnlAchievements == null) return;
            pnlAchievements.Controls.Clear();

            // Calculate achievements manually
            bool hasFirstWin = false;
            bool hasHighRoller = false;
            bool hasMillionaire = false;
            bool hasSpeedDemon = false;
            int millionaireCount = 0;

            foreach (var result in results)
            {
                if (result.AmountWon > 0) hasFirstWin = true;
                if (result.AmountWon >= 100000) hasHighRoller = true;
                if (result.AmountWon >= 1000000)
                {
                    hasMillionaire = true;
                    millionaireCount++;
                }
                if (result.GameDuration <= TimeSpan.FromMinutes(3)) hasSpeedDemon = true;
            }

            // Check achievements manually
            string[] achievementEmojis = { "🥉", "🥈", "🥇", "⚡", "🎯", "🔥", "💎" };
            string[] achievementNames = { "First Win", "High Roller", "Millionaire", "Speed Demon", "Perfectionist", "Hot Streak", "Diamond Player" };
            bool[] achievementUnlocked = { hasFirstWin, hasHighRoller, hasMillionaire, hasSpeedDemon, millionaireCount >= 5, results.Count >= 10, results.Count >= 50 };

            for (int i = 0; i < achievementEmojis.Length; i++)
            {
                string emoji = achievementEmojis[i];
                string name = achievementNames[i];
                bool unlocked = achievementUnlocked[i];

                var achievementLabel = new Label
                {
                    Text = $"{emoji} {name}",
                    Size = new Size(120, 30),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Arial", 9, FontStyle.Bold),
                    ForeColor = unlocked ? Color.FromArgb(255, 215, 0) : Color.Gray,
                    BackColor = unlocked ? Color.FromArgb(34, 47, 86) : Color.FromArgb(60, 60, 60),
                    BorderStyle = BorderStyle.FixedSingle
                };

                pnlAchievements.Controls.Add(achievementLabel);
            }
        }

        private void ShowEmptyStats()
        {
            if (lblTotalGames != null) lblTotalGames.Text = "Total Games Played: 0";
            if (lblTotalWinnings != null) lblTotalWinnings.Text = "Total Winnings: $0";
            if (lblAverageScore != null) lblAverageScore.Text = "Average Score: $0";
            if (lblBestScore != null) lblBestScore.Text = "Best Score: $0";
            if (lblSuccessRate != null) lblSuccessRate.Text = "Success Rate: 0%";
            if (lblAverageTime != null) lblAverageTime.Text = "Average Game Time: 00:00";
            if (lblBestTime != null) lblBestTime.Text = "Best Game Time: 00:00";
            if (lblTotalPlayTime != null) lblTotalPlayTime.Text = "Total Play Time: 00:00";
            if (progressOverall != null) progressOverall.Value = 0;

            if (pnlAchievements != null)
            {
                pnlAchievements.Controls.Clear();
                var emptyLabel = new Label
                {
                    Text = "No games played yet!\nStart playing to see your statistics here.",
                    Size = new Size(200, 50),
                    TextAlign = ContentAlignment.MiddleCenter,
                    ForeColor = Color.Gray,
                    Font = new Font("Arial", 10)
                };
                pnlAchievements.Controls.Add(emptyLabel);
            }
        }

        private void BtnReset_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "Are you sure you want to reset all statistics? This will also clear all game results and cannot be undone.",
                "Reset Statistics",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                try
                {
                    string resultsFilePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "game_results.json");
                    if (System.IO.File.Exists(resultsFilePath))
                    {
                        System.IO.File.Delete(resultsFilePath);
                        LoadStatistics(); // Refresh the display
                        MessageBox.Show("All statistics have been reset.", "Reset Complete",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                catch (Exception ex)
                {
                    Logger.LogException(ex);
                    MessageBox.Show("Error resetting statistics.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void BtnClose_Click(object? sender, EventArgs e)
        {
            this.Close();
        }
    }
}
