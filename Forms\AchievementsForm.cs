using System;
using System.Drawing;
using System.Windows.Forms;
using MillionaireGame.Classes;

namespace MillionaireGame.Forms
{
    public partial class AchievementsForm : Form
    {
        private ListView? achievementListView;
        private Label? titleLabel;

        public AchievementsForm()
        {
            InitializeComponent();
            LoadAchievements();
        }

        private void InitializeComponent()
        {
            this.Text = "Achievements";
            this.Size = new Size(700, 500);
            this.StartPosition = FormStartPosition.CenterParent;
            this.BackColor = Color.FromArgb(20, 30, 50);

            // Title Label
            titleLabel = new Label
            {
                Text = "🏆 Achievements 🏆",
                Location = new Point(0, 20),
                Size = new Size(this.Width, 60),
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.Gold,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Achievement List View
            achievementListView = new ListView
            {
                Location = new Point(30, 100),
                Size = new Size(this.Width - 60, this.Height - 150),
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                BackColor = Color.FromArgb(30, 40, 60),
                ForeColor = Color.White,
                Font = new Font("Segoe UI", 11),
                OwnerDraw = true
            };

            achievementListView.DrawColumnHeader += AchievementListView_DrawColumnHeader;
            achievementListView.DrawItem += AchievementListView_DrawItem;
            achievementListView.DrawSubItem += AchievementListView_DrawSubItem;

            // Define columns
            achievementListView.Columns.Add("Achievement", 200);
            achievementListView.Columns.Add("Description", 350);
            achievementListView.Columns.Add("Unlocked", 100);

            this.Controls.Add(titleLabel);
            this.Controls.Add(achievementListView);
        }

        private void LoadAchievements()
        {
            if (achievementListView == null) return;

            achievementListView.Items.Clear();
            var achievements = AchievementManager.GetAllAchievements();

            foreach (var achievement in achievements)
            {
                var item = new ListViewItem(achievement.Name);
                item.SubItems.Add(achievement.Description);
                item.SubItems.Add(achievement.IsUnlocked ? "✔️" : "❌");
                item.Tag = achievement.IsUnlocked; // Store unlocked status in Tag

                // Set colors based on unlocked status
                if (achievement.IsUnlocked)
                {
                    item.ForeColor = Color.FromArgb(173, 255, 47); // GreenYellow
                    item.Font = new Font(achievementListView!.Font, FontStyle.Bold);
                }
                else
                {
                    item.ForeColor = Color.Gray;
                }

                achievementListView.Items.Add(item);
            }
        }

        private void AchievementListView_DrawColumnHeader(object? sender, DrawListViewColumnHeaderEventArgs e)
        {
            using (var sf = new StringFormat())
            {
                sf.Alignment = StringAlignment.Center;
                sf.LineAlignment = StringAlignment.Center;

                using (var headerBrush = new SolidBrush(Color.FromArgb(50, 60, 80)))
                {
                    e.Graphics.FillRectangle(headerBrush, e.Bounds);
                }
                if (e.Header != null && achievementListView != null)
                {
                    e.Graphics.DrawString(e.Header.Text, achievementListView.Font, Brushes.White, e.Bounds, sf);
                }
            }
        }

        private void AchievementListView_DrawSubItem(object? sender, DrawListViewSubItemEventArgs e)
        {
            e.DrawDefault = true;

            // Custom drawing for the "Unlocked" column to center the check/cross
            if (e.ColumnIndex == 2)
            {
                using (var cellBrush = new SolidBrush(Color.FromArgb(30, 40, 60)))
                {
                    e.Graphics.FillRectangle(cellBrush, e.Bounds); // Cell background
                }

                if (e.SubItem != null && e.Item != null && e.Item.Tag is bool isUnlocked)
                {
                    string text = e.SubItem.Text;
                    Color textColor = isUnlocked ? Color.FromArgb(173, 255, 47) : Color.Red;

                    using (var brush = new SolidBrush(textColor))
                    using (var sf = new StringFormat())
                    {
                        sf.Alignment = StringAlignment.Center;
                        sf.LineAlignment = StringAlignment.Center;
                        if (achievementListView != null)
                        {
                            e.Graphics.DrawString(text, achievementListView.Font, brush, e.Bounds, sf);
                        }
                    }
                }
            }
        }

        private void AchievementListView_DrawItem(object? sender, DrawListViewItemEventArgs e)
        {
            e.DrawDefault = false;
            e.DrawBackground();

            // Custom drawing for the entire row
            if ((e.State & ListViewItemStates.Selected) != 0)
            {
                using (var selectedBrush = new SolidBrush(Color.FromArgb(50, 60, 80)))
                {
                    e.Graphics.FillRectangle(selectedBrush, e.Bounds);
                }
            }
            else
            {
                 using (var rowBrush = new SolidBrush(Color.FromArgb(30, 40, 60)))
                {
                    e.Graphics.FillRectangle(rowBrush, e.Bounds);
                }
            }

            for (int i = 0; i < e.Item.SubItems.Count; i++)
            {
                var subItem = e.Item.SubItems[i];
                if (subItem != null)
                {
                    var bounds = e.Item.GetSubItemAt(e.Item.Position.X + subItem.Bounds.X, e.Item.Position.Y + subItem.Bounds.Y)!.Bounds;

                    if (i == 2 && e.Item.Tag is bool isUnlocked) // "Unlocked" column
                    {
                        string text = subItem.Text;
                        Color textColor = isUnlocked ? Color.FromArgb(173, 255, 47) : Color.Red;
                        using (var brush = new SolidBrush(textColor))
                        using (var sf = new StringFormat())
                        {
                            sf.Alignment = StringAlignment.Center;
                            sf.LineAlignment = StringAlignment.Center;
                            e.Graphics.DrawString(text, e.Item.Font, brush, bounds, sf);
                        }
                    }
                    else
                    {
                        TextRenderer.DrawText(e.Graphics, subItem.Text, e.Item.Font, bounds, e.Item.ForeColor, TextFormatFlags.Left | TextFormatFlags.VerticalCenter);
                    }
                }
            }
        }
    }
}
