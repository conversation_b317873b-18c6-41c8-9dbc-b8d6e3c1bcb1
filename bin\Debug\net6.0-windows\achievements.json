[{"Id": "first_game", "Name": "First Steps", "Description": "Play your first game", "Icon": "🎮", "Points": 10, "UnlockedDate": "2025-10-03T22:38:51.795454+03:00", "IsUnlocked": true, "Category": 0, "Rarity": 0}, {"Id": "first_win", "Name": "First Victory", "Description": "Win your first game with any amount", "Icon": "🥉", "Points": 25, "UnlockedDate": null, "IsUnlocked": false, "Category": 0, "Rarity": 0}, {"Id": "welcome_aboard", "Name": "Welcome Aboard", "Description": "Complete the tutorial or help section", "Icon": "👋", "Points": 5, "UnlockedDate": null, "IsUnlocked": false, "Category": 0, "Rarity": 0}, {"Id": "thousand_club", "Name": "Thousand Club", "Description": "Win at least $1,000", "Icon": "💰", "Points": 50, "UnlockedDate": null, "IsUnlocked": false, "Category": 1, "Rarity": 0}, {"Id": "high_roller", "Name": "High Roller", "Description": "Win at least $100,000", "Icon": "💎", "Points": 100, "UnlockedDate": null, "IsUnlocked": false, "Category": 1, "Rarity": 1}, {"Id": "millionaire", "Name": "Millionaire!", "Description": "Win the ultimate prize of $1,000,000", "Icon": "👑", "Points": 500, "UnlockedDate": null, "IsUnlocked": false, "Category": 1, "Rarity": 3}, {"Id": "multi_millionaire", "Name": "Multi-Millionaire", "Description": "Win $1,000,000 three times", "Icon": "💸", "Points": 1000, "UnlockedDate": null, "IsUnlocked": false, "Category": 1, "Rarity": 4}, {"Id": "speed_demon", "Name": "Speed Demon", "Description": "Complete a game in under 3 minutes", "Icon": "⚡", "Points": 75, "UnlockedDate": null, "IsUnlocked": false, "Category": 2, "Rarity": 1}, {"Id": "lightning_fast", "Name": "Lightning Fast", "Description": "Answer 5 questions in under 30 seconds total", "Icon": "🌩️", "Points": 100, "UnlockedDate": null, "IsUnlocked": false, "Category": 2, "Rarity": 2}, {"Id": "quick_thinker", "Name": "Quick Thinker", "Description": "Answer a question in under 5 seconds", "Icon": "🧠", "Points": 25, "UnlockedDate": null, "IsUnlocked": false, "Category": 2, "Rarity": 0}, {"Id": "persistent_player", "Name": "Persistent Player", "Description": "Play 10 games", "Icon": "🔥", "Points": 50, "UnlockedDate": null, "IsUnlocked": false, "Category": 3, "Rarity": 0}, {"Id": "dedicated_gamer", "Name": "Dedicated Gamer", "Description": "Play 50 games", "Icon": "🎯", "Points": 150, "UnlockedDate": null, "IsUnlocked": false, "Category": 3, "Rarity": 1}, {"Id": "game_master", "Name": "Game Master", "Description": "Play 100 games", "Icon": "🏆", "Points": 300, "UnlockedDate": null, "IsUnlocked": false, "Category": 3, "Rarity": 2}, {"Id": "legend", "Name": "Legend", "Description": "Play 500 games", "Icon": "⭐", "Points": 1000, "UnlockedDate": null, "IsUnlocked": false, "Category": 3, "Rarity": 4}, {"Id": "perfectionist", "Name": "Perfectionist", "Description": "Win 5 games in a row", "Icon": "✨", "Points": 200, "UnlockedDate": null, "IsUnlocked": false, "Category": 4, "Rarity": 2}, {"Id": "unstoppable", "Name": "Unstoppable", "Description": "Win 10 games in a row", "Icon": "🚀", "Points": 500, "UnlockedDate": null, "IsUnlocked": false, "Category": 4, "Rarity": 3}, {"Id": "knowledge_master", "Name": "Knowledge Master", "Description": "Answer 100 questions correctly", "Icon": "📚", "Points": 100, "UnlockedDate": null, "IsUnlocked": false, "Category": 4, "Rarity": 1}, {"Id": "trivia_expert", "Name": "Trivia Expert", "Description": "Answer 500 questions correctly", "Icon": "🎓", "Points": 300, "UnlockedDate": null, "IsUnlocked": false, "Category": 4, "Rarity": 2}, {"Id": "lifeline_master", "Name": "Lifeline Master", "Description": "Use all lifelines in a single game and still win", "Icon": "🆘", "Points": 150, "UnlockedDate": null, "IsUnlocked": false, "Category": 5, "Rarity": 2}, {"Id": "no_lifelines", "Name": "Pure Knowledge", "Description": "Win without using any lifelines", "Icon": "🧙", "Points": 200, "UnlockedDate": null, "IsUnlocked": false, "Category": 5, "Rarity": 2}, {"Id": "comeback_king", "Name": "Comeback King", "Description": "Win after using all lifelines", "Icon": "👑", "Points": 100, "UnlockedDate": null, "IsUnlocked": false, "Category": 5, "Rarity": 1}, {"Id": "risk_taker", "Name": "Risk Taker", "Description": "Continue playing after reaching $32,000 safe haven", "Icon": "🎲", "Points": 75, "UnlockedDate": null, "IsUnlocked": false, "Category": 5, "Rarity": 0}, {"Id": "early_bird", "Name": "Early Bird", "Description": "Play a game before 8 AM", "Icon": "🌅", "Points": 25, "UnlockedDate": null, "IsUnlocked": false, "Category": 5, "Rarity": 0}, {"Id": "night_owl", "Name": "Night Owl", "Description": "Play a game after 11 PM", "Icon": "🦉", "Points": 25, "UnlockedDate": null, "IsUnlocked": false, "Category": 5, "Rarity": 0}, {"Id": "weekend_warrior", "Name": "Weekend Warrior", "Description": "Play 10 games on weekends", "Icon": "🏖️", "Points": 50, "UnlockedDate": null, "IsUnlocked": false, "Category": 5, "Rarity": 0}, {"Id": "daily_player", "Name": "Daily Player", "Description": "Play at least one game for 7 consecutive days", "Icon": "📅", "Points": 100, "UnlockedDate": null, "IsUnlocked": false, "Category": 5, "Rarity": 1}]