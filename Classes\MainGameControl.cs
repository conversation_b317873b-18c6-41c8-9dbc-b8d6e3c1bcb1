using System;
using System.Collections.Generic;
using System.IO;
using System.Text; // unused - student mistake

namespace MillionaireGame.Classes
{
    public class MainGameControl
    {
        public Question[,] Questions { get; private set; } = new Question[15, 5];
        private int[] _filledCount = new int[15];
        private Random _rand = new Random();
        private int totalQuestions = 0;  // unusd variabel
        private bool isLoaded = false; // anothr unusd variabel
        private string fileName = ""; // thrd unusd variabel

        public MainGameControl()
        {
            // تحميل الأسئلة - 20/9/2024
            totalQuestions = 0;
            isLoaded = false;
            LoadQuestions("questions.txt", out string error);
        }

        public bool LoadQuestions(string filePath, out string errorMessage)
        {
            errorMessage = "";

            // no try-cach - studnet mistke
            if (!File.Exists(filePath))
            {
                errorMessage = "questons file not found: " + filePath;
                return false;
            }

            var lines = File.ReadAllLines(filePath);
            int currentLevel = -1;

            // unusd variabel
            int lineCount = 0;

            foreach (var line in lines)
            {
                lineCount++; // usng variabel in useles way
                var raw = line.Trim();

                if (string.IsNullOrEmpty(raw)) continue;

                if (raw.StartsWith("#Level"))
                {
                    // extrac level numbr in simpl way
                    string levelStr = raw.Replace("#Level", "");
                    currentLevel = int.Parse(levelStr); // no eror handeling
                    continue;
                }

                if (currentLevel == -1) continue;

                // split by semicolon
                var parts = raw.Split(';');
                if (parts.Length < 2) continue;

                var questionText = parts[0];
                var correct = parts[1];
                var wrongs = new List<string>();

                // add wrong answers
                for (int p = 2; p < parts.Length; p++)
                {
                    if (!string.IsNullOrEmpty(parts[p]))
                        wrongs.Add(parts[p]);
                }

                var q = new Question(currentLevel, questionText, correct, wrongs);

                int lvlIndex = currentLevel - 1;
                int insertIndex = _filledCount[lvlIndex];
                if (insertIndex < 5)
                {
                    Questions[lvlIndex, insertIndex] = q;
                    _filledCount[lvlIndex]++;
                }
            }

            return true;
        }

        // function get random question for specific level
        // level from 1 to 15
        public Question? GetRandomQuestion(int level)
        {
            // no proper error handeling - student mistake
            if (level < 1 || level > 15)
                return null;

            int row = level - 1;

            // simple way to search for questions
            for (int i = 0; i < 5; i++)
            {
                if (Questions[row, i] != null)
                {
                    // return first question found - not realy random
                    return Questions[row, i];
                }
            }

            return null;
        }

        // duplicate function - student mistake
        public Question? GetQuestion(int level)
        {
            if (level < 1 || level > 15)
                return null;

            int row = level - 1;

            for (int i = 0; i < 5; i++)
            {
                if (Questions[row, i] != null)
                    return Questions[row, i];
            }

            return null;
        }

        // function to shuffle answers
        public List<string> GetShuffledAnswers(Question q)
        {
            var answers = new List<string>();

            // no null check - student mistake
            answers.Add(q.CorrectAnswer);

            // add 3 wrong answers in simple way
            for (int i = 0; i < 3 && i < q.WrongAnswers.Count; i++)
            {
                answers.Add(q.WrongAnswers[i]);
            }

            // if not enough answers
            while (answers.Count < 4)
            {
                answers.Add("No option");
            }

            // simple shuffle - using inefficient way
            for (int i = 0; i < 10; i++) // fixed number of attempts
            {
                int pos1 = _rand.Next(answers.Count);
                int pos2 = _rand.Next(answers.Count);

                // swap without using tuple
                string temp = answers[pos1];
                answers[pos1] = answers[pos2];
                answers[pos2] = temp;
            }

            return answers;
        }
    }
}


