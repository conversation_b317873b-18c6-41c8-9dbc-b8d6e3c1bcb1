using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Windows.Forms;
using MillionaireGame.Classes;
using MillionaireGame.Forms;

namespace MillionaireGame
{
    public class StartForm : Form
    {
        private MainGameControl _gm;

        private Panel? topPanel;
        private Panel? backgroundPanel;
        private Panel? buttonsPanel;
        private Label? lblTitle;
        private Label? lblSubtitle;
        private Button? btnStart;
        private Button? btnHighScores;
        private Button? btnSettings;
        private Button? btnHelp;
        private Button? btnExit;
        private Button? btnStatistics;
        private Label? lblStatus;

        public StartForm(MainGameControl gm)
        {
            // simple constructor
            _gm = gm;
            InitializeComponents();
        }

        private void InitializeComponents()
        {
            this.Text = "Who Wants to Be a Millionaire - Start";
            this.Width = 1000;
            this.Height = 700;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(15, 25, 45);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            // Background panel with gradient
            backgroundPanel = new Panel()
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(15, 25, 45)
            };
            backgroundPanel.Paint += BackgroundPanel_Paint;

            // Top panel with title
            topPanel = new Panel()
            {
                Location = new Point(0, 0),
                Size = new Size(1000, 120),
                BackColor = Color.Transparent
            };

            // Buttons panel for organized layout
            buttonsPanel = new Panel()
            {
                Location = new Point(150, 180),
                Size = new Size(700, 350),
                BackColor = Color.FromArgb(20, 255, 255, 255) // Semi-transparent white for debugging
            };

            // Main title
            lblTitle = new Label()
            {
                Text = "🎯 Who Wants to Be a Millionaire?",
                Location = new Point(50, 20),
                Size = new Size(900, 50),
                Font = new Font("Arial", 28, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 215, 0), // Gold
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Subtitle
            lblSubtitle = new Label()
            {
                Text = "The Ultimate Trivia Challenge",
                Location = new Point(50, 70),
                Size = new Size(900, 25),
                Font = new Font("Arial", 14, FontStyle.Italic),
                ForeColor = Color.FromArgb(200, 200, 255),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Create buttons with consistent styling
            btnStart = CreateStyledButton("🎮 New Game", new Point(65, 30), Color.FromArgb(0, 150, 255));
            btnStart.Click += BtnStart_Click;

            btnHighScores = CreateStyledButton("🏆 High Scores", new Point(65, 120), Color.FromArgb(255, 140, 0));
            btnHighScores.Click += BtnHighScores_Click;

            btnSettings = CreateStyledButton("⚙️ Settings", new Point(65, 210), Color.FromArgb(128, 0, 128));
            btnSettings.Click += BtnSettings_Click;

            btnHelp = CreateStyledButton("❓ Help", new Point(375, 30), Color.FromArgb(0, 128, 0));
            btnHelp.Click += BtnHelp_Click;

            btnStatistics = CreateStyledButton("📊 Statistics", new Point(375, 120), Color.FromArgb(40, 167, 69));
            btnStatistics.Click += BtnStatistics_Click;

            btnExit = CreateStyledButton("🚪 Exit", new Point(375, 210), Color.FromArgb(220, 20, 60));
            btnExit.Click += BtnExit_Click;

            // Status label
            lblStatus = new Label()
            {
                Text = "Choose from the menu above",
                Location = new Point(50, 580),
                Size = new Size(900, 30),
                Font = new Font("Arial", 12),
                ForeColor = Color.FromArgb(180, 180, 255),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Add controls to panels
            topPanel.Controls.Add(lblTitle);
            topPanel.Controls.Add(lblSubtitle);

            buttonsPanel.Controls.Add(btnStart);
            buttonsPanel.Controls.Add(btnHighScores);
            buttonsPanel.Controls.Add(btnSettings);
            buttonsPanel.Controls.Add(btnHelp);
            buttonsPanel.Controls.Add(btnStatistics);
            buttonsPanel.Controls.Add(btnExit);

            backgroundPanel.Controls.Add(topPanel);
            backgroundPanel.Controls.Add(buttonsPanel);
            backgroundPanel.Controls.Add(lblStatus);

            this.Controls.Add(backgroundPanel);
        }

        private Button CreateStyledButton(string text, Point location, Color backColor)
        {
            var button = new Button()
            {
                Text = text,
                Location = location,
                Size = new Size(260, 70),
                Font = new Font("Arial", 12, FontStyle.Bold),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.BorderColor = button.BackColor;

            // Add hover effects using regular event handlers
            button.MouseEnter += StartButton_MouseEnter;
            button.MouseLeave += StartButton_MouseLeave;

            // Store colors in Tag for later use
            button.Tag = new Color[] { backColor, Color.FromArgb(
                Math.Min(255, backColor.R + 30),
                Math.Min(255, backColor.G + 30),
                Math.Min(255, backColor.B + 30)
            ) };

            return button;
        }

        private void StartButton_MouseEnter(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Color[] colors)
            {
                button.BackColor = colors[1]; // hover color
            }
        }

        private void StartButton_MouseLeave(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Color[] colors)
            {
                button.BackColor = colors[0]; // base color
            }
        }

        private void BackgroundPanel_Paint(object? sender, PaintEventArgs e)
        {
            // Create gradient background
            using (LinearGradientBrush brush = new LinearGradientBrush(
                backgroundPanel!.ClientRectangle,
                Color.FromArgb(15, 25, 45),
                Color.FromArgb(25, 35, 65),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, backgroundPanel.ClientRectangle);
            }

            // Add some decorative elements
            using (Pen pen = new Pen(Color.FromArgb(50, 255, 215, 0), 2))
            {
                // Draw decorative border
                Rectangle rect = new Rectangle(20, 20, backgroundPanel.Width - 40, backgroundPanel.Height - 40);
                e.Graphics.DrawRectangle(pen, rect);

                // Draw corner decorations
                int cornerSize = 30;
                e.Graphics.DrawLine(pen, 20, 20 + cornerSize, 20 + cornerSize, 20);
                e.Graphics.DrawLine(pen, backgroundPanel.Width - 20 - cornerSize, 20, backgroundPanel.Width - 20, 20 + cornerSize);
                e.Graphics.DrawLine(pen, 20, backgroundPanel.Height - 20 - cornerSize, 20 + cornerSize, backgroundPanel.Height - 20);
                e.Graphics.DrawLine(pen, backgroundPanel.Width - 20 - cornerSize, backgroundPanel.Height - 20, backgroundPanel.Width - 20, backgroundPanel.Height - 20 - cornerSize);
            }
        }

        private void BtnStart_Click(object? sender, EventArgs e)
        {
            string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "questions.txt");
            if (!_gm.LoadQuestions(path, out string err))
            {
                MessageBox.Show("Failed to load questions:\n" + err, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus!.Text = "Error loading questions";
                return;
            }

            lblStatus!.Text = "Starting game...";

            var qform = new QuestionForm(_gm);
            qform.FormClosed += QuestionForm_FormClosed;
            qform.Show();
        }

        private void QuestionForm_FormClosed(object? sender, FormClosedEventArgs e)
        {
            // Check if ResultForm is open
            bool hasResultForm = false;
            foreach (Form form in Application.OpenForms)
            {
                if (form is ResultForm)
                {
                    hasResultForm = true;
                    break;
                }
            }

            // Show start form again if no result form
            if (!hasResultForm)
            {
                this.Show();
            }
        }

        private void BtnHighScores_Click(object? sender, EventArgs e)
        {
            lblStatus!.Text = "Opening High Scores";
            var highScoresForm = new HighScoresForm();
            highScoresForm.ShowDialog();
            lblStatus.Text = "Choose from menu";
        }

        private void BtnSettings_Click(object? sender, EventArgs e)
        {
            lblStatus!.Text = "Opening Settings...";
            var settingsForm = new SettingsForm();
            settingsForm.ShowDialog();
            lblStatus.Text = "اختر من القائمة أعلاه - Choose from the menu above";
        }

        private void BtnHelp_Click(object? sender, EventArgs e)
        {
            lblStatus!.Text = "Opening Help...";
            var helpForm = new HelpForm();
            helpForm.ShowDialog();
            lblStatus.Text = "اختر من القائمة أعلاه - Choose from the menu above";
        }

        private void BtnStatistics_Click(object? sender, EventArgs e)
        {
            lblStatus!.Text = "Opening Statistics...";
            var statisticsForm = new StatisticsForm();
            statisticsForm.ShowDialog();
            lblStatus.Text = "Choose from the menu above";
        }

        private void BtnExit_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد الخروج من اللعبة؟\nDo you want to exit the game?",
                "تأكيد الخروج - Confirm Exit",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            base.OnFormClosed(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            try
            {
                File.AppendAllText("debug_log.txt", "Form OnLoad called\n");
                base.OnLoad(e);
                File.AppendAllText("debug_log.txt", "Form OnLoad completed\n");
            }
            catch (Exception ex)
            {
                File.AppendAllText("debug_log.txt", $"Error in OnLoad: {ex.Message}\n{ex.StackTrace}\n");
                throw;
            }
        }

        protected override void OnShown(EventArgs e)
        {
            try
            {
                File.AppendAllText("debug_log.txt", "Form OnShown called\n");
                base.OnShown(e);
                File.AppendAllText("debug_log.txt", "Form OnShown completed\n");
            }
            catch (Exception ex)
            {
                File.AppendAllText("debug_log.txt", $"Error in OnShown: {ex.Message}\n{ex.StackTrace}\n");
                throw;
            }
        }
    }
}

