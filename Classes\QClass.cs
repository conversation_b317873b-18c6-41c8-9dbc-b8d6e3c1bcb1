using System;

namespace MillionaireGame.Classes
{
    public abstract class QuestionBase
    {
        public int Level { get; protected set; }
        public string Text { get; protected set; }

        protected QuestionBase(int level, string text)
        {
            Level = level;
            Text = text?.Trim() ?? string.Empty;
        }

        public abstract bool IsCorrect(string answer);
    }
}

