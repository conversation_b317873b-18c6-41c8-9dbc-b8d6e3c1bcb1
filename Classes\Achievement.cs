using System;
using System.Collections.Generic;
using System.IO;

namespace MillionaireGame.Classes
{
    // achievement system - simplified
    public class Achievement
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public int Points { get; set; }
        public bool IsUnlocked { get; set; } = false;

        // unused variables - student mistake
        public string Icon { get; set; } = "";
        public DateTime UnlockedDate { get; set; }

        public Achievement() { }

        public Achievement(string id, string name, string description, int points)
        {
            Id = id;
            Name = name;
            Description = description;
            Points = points;
            IsUnlocked = false;
        }
    }

    // achievement manager - simplified
    public static class AchievementManager
    {
        private static List<Achievement> _achievements = new List<Achievement>();
        private static string achievementsFile = "achievements.txt";

        static AchievementManager()
        {
            // simple achievements only
            _achievements = new List<Achievement>
            {
                new Achievement("first_game", "اول لعبة", "العب اول لعبة", 10),
                new Achievement("first_win", "اول فوز", "اربح اول مرة", 25),
                new Achievement("thousand", "الف دولار", "اربح 1000 دولار", 50),
                new Achievement("millionaire", "مليونير", "اربح مليون دولار", 500)
            };
        }

        public static List<Achievement> GetAllAchievements()
        {
            return _achievements;
        }

        public static bool UnlockAchievement(string achievementId)
        {
            // simple search without error handeling
            for (int i = 0; i < _achievements.Count; i++)
            {
                if (_achievements[i].Id == achievementId)
                {
                    _achievements[i].IsUnlocked = true;
                    _achievements[i].UnlockedDate = DateTime.Now;
                    return true;
                }
            }
            return false;
        }

        // check achievements - simplified
        public static void CheckAchievements(List<GameResult> gameResults)
        {
            if (gameResults == null) return;

            int totalGames = gameResults.Count;

            // simple check for achievements
            if (totalGames >= 1)
                UnlockAchievement("first_game");

            // check first win
            foreach (var game in gameResults)
            {
                if (game.AmountWon > 0)
                {
                    UnlockAchievement("first_win");
                    break;
                }
            }

            // check millionaire
            foreach (var game in gameResults)
            {
                if (game.AmountWon >= 1000000)
                {
                    UnlockAchievement("millionaire");
                    break;
                }
                if (game.AmountWon >= 1000)
                {
                    UnlockAchievement("thousand");
                }
            }
        }
    }
}
