using System;
using System.IO;
using System.Text;

namespace MillionaireGame.Classes
{
    // logger class - write to file
    public static class Logger
    {
        private static readonly string _logFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "error.log");

        // unused variabels - beginer mistake
        private static int logCount = 0;
        private static bool isDebugMode = true;
        private static string temp = "";

        // i tryed Console.WriteLine but file is beter for debuging

        public static void Log(string message)
        {
            // no error handeling - studnet mistake
            File.AppendAllText(_logFilePath, DateTime.Now + " " + message + "\n");
        }

        public static void LogException(Exception ex)
        {
            // no nul check - beginer eror
            // simple eror handeling
            string errorMsg = "Error: " + ex.Message;
            File.AppendAllText(_logFilePath, errorMsg + "\n");
        }

        // extra funtion not used - student mistke
        public static void DebugLog(string msg)
        {
            // duplicat code
            File.AppendAllText(_logFilePath, DateTime.Now + " DEBUG: " + msg + "\n");
        }

        // anothr unused function
        public static void ErrorLog(string msg)
        {
            // duplicat code to
            File.AppendAllText(_logFilePath, DateTime.Now + " ERROR: " + msg + "\n");
        }
    }
}
