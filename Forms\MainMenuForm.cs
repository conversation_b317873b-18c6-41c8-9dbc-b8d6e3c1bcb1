using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Windows.Forms;
using MillionaireGame.Classes;
using MillionaireGame.Forms;

namespace MillionaireGame
{
    public class MainMenuForm : Form
    {
        private Panel? backgroundPanel;
        private Label? lblTitle;
        private Label? lblSubtitle;
        private Button? btnNewGame;
        private Button? btnHighScores;
        private Button? btnSettings;
        private Button? btnStatistics;
        private Button? btnAchievements;
        private Button? btnHelp;
        private Button? btnExit;
        private MainGameControl _gameManager;

        public MainMenuForm()
        {
            _gameManager = new MainGameControl();

            // load questions
            string questionsPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "questions.txt");
            if (!_gameManager.LoadQuestions(questionsPath, out string errorMessage))
            {
                Logger.Log($"Failed to load questions: {errorMessage}");
                MessageBox.Show($"Failed to load questions file:\n{errorMessage}\n\nThe game may not work properly.",
                              "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
            else
            {
                Logger.Log("Questions loaded successfully from: " + questionsPath);
            }

            InitializeComponents();
        }

        private void InitializeComponents()
        {
            // Form settings
            this.Text = "Who Wants to Be a Millionaire";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.BackColor = Color.FromArgb(15, 25, 45);

            // Background panel with gradient
            backgroundPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.Transparent
            };
            backgroundPanel.Paint += BackgroundPanel_Paint;

            // Title with glow effect
            lblTitle = new Label
            {
                Text = "WHO WANTS TO BE A",
                Location = new Point(50, 80),
                Size = new Size(700, 60),
                Font = new Font("Arial Black", 28, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 215, 0),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            lblSubtitle = new Label
            {
                Text = "MILLIONAIRE?",
                Location = new Point(50, 140),
                Size = new Size(700, 80),
                Font = new Font("Arial Black", 36, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 255, 255),
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.Transparent
            };

            // Menu buttons with modern design
            int buttonWidth = 250;
            int buttonHeight = 50;
            int startY = 250;
            int spacing = 60;

            btnNewGame = CreateMenuButton("🎮 New Game", startY, buttonWidth, buttonHeight, Color.FromArgb(76, 175, 80));
            btnHighScores = CreateMenuButton("🏆 High Scores", startY + spacing, buttonWidth, buttonHeight, Color.FromArgb(255, 193, 7));
            btnStatistics = CreateMenuButton("📊 Statistics", startY + spacing * 2, buttonWidth, buttonHeight, Color.FromArgb(33, 150, 243));
            btnAchievements = CreateMenuButton("🏅 Achievements", startY + spacing * 3, buttonWidth, buttonHeight, Color.FromArgb(255, 152, 0));
            btnSettings = CreateMenuButton("⚙️ Settings", startY + spacing * 4, buttonWidth, buttonHeight, Color.FromArgb(156, 39, 176));
            btnHelp = CreateMenuButton("❓ Help", startY + spacing * 5, buttonWidth, buttonHeight, Color.FromArgb(255, 87, 34));
            btnExit = CreateMenuButton("❌ Exit", startY + spacing * 6, buttonWidth, buttonHeight, Color.FromArgb(244, 67, 54));

            // Event handlers
            btnNewGame.Click += BtnNewGame_Click;
            btnHighScores.Click += BtnHighScores_Click;
            btnStatistics.Click += BtnStatistics_Click;
            btnAchievements.Click += BtnAchievements_Click;
            btnSettings.Click += BtnSettings_Click;
            btnHelp.Click += BtnHelp_Click;
            btnExit.Click += BtnExit_Click;

            // Add controls
            backgroundPanel.Controls.Add(lblTitle);
            backgroundPanel.Controls.Add(lblSubtitle);
            backgroundPanel.Controls.Add(btnNewGame);
            backgroundPanel.Controls.Add(btnHighScores);
            backgroundPanel.Controls.Add(btnStatistics);
            backgroundPanel.Controls.Add(btnAchievements);
            backgroundPanel.Controls.Add(btnSettings);
            backgroundPanel.Controls.Add(btnHelp);
            backgroundPanel.Controls.Add(btnExit);

            this.Controls.Add(backgroundPanel);
        }

        private Button CreateMenuButton(string text, int y, int width, int height, Color baseColor)
        {
            var button = new Button
            {
                Text = text,
                Location = new Point((this.Width - width) / 2, y),
                Size = new Size(width, height),
                Font = new Font("Arial", 14, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = baseColor,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(baseColor, 0.2f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(baseColor, 0.2f);

            // Add hover effects using regular event handlers (no lambda)
            button.MouseEnter += Button_MouseEnter;
            button.MouseLeave += Button_MouseLeave;

            // Store base color in Tag for later use
            button.Tag = baseColor;

            return button;
        }

        private void Button_MouseEnter(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Color baseColor)
            {
                button.BackColor = ControlPaint.Light(baseColor, 0.2f);
                button.Font = new Font("Arial", 15, FontStyle.Bold);
            }
        }

        private void Button_MouseLeave(object? sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Color baseColor)
            {
                button.BackColor = baseColor;
                button.Font = new Font("Arial", 14, FontStyle.Bold);
            }
        }

        private void BackgroundPanel_Paint(object? sender, PaintEventArgs e)
        {
            // gradient background
            using (LinearGradientBrush brush = new LinearGradientBrush(
                backgroundPanel!.ClientRectangle,
                Color.FromArgb(15, 25, 45),
                Color.FromArgb(25, 35, 65),
                LinearGradientMode.Vertical))
            {
                e.Graphics.FillRectangle(brush, backgroundPanel.ClientRectangle);
            }

            // Add static stars (no animation)
            DrawStaticStars(e.Graphics);
        }

        private void DrawStaticStars(Graphics g)
        {
            // draw stars
            Random rand = new Random(42); // fixed seed for same positions
            using (SolidBrush starBrush = new SolidBrush(Color.FromArgb(200, 255, 255, 255)))
            {
                for (int i = 0; i < 50; i++)
                {
                    int x = rand.Next(0, backgroundPanel!.Width);
                    int y = rand.Next(0, backgroundPanel!.Height);
                    int size = rand.Next(1, 4);

                    g.FillEllipse(starBrush, x, y, size, size);
                }
            }
        }

        // Event handlers
        private void BtnNewGame_Click(object? sender, EventArgs e)
        {
            // SoundManager.PlayButtonClick();
            Logger.Log("Starting new game from main menu");
            var questionForm = new QuestionForm(_gameManager);
            questionForm.Show();
            this.Hide();
        }

        private void BtnHighScores_Click(object? sender, EventArgs e)
        {
            // SoundManager.PlayButtonClick();
            var highScoresForm = new HighScoresForm();
            highScoresForm.ShowDialog();
        }

        private void BtnStatistics_Click(object? sender, EventArgs e)
        {
            // SoundManager.PlayButtonClick();
            var statisticsForm = new StatisticsForm();
            statisticsForm.ShowDialog();
        }

        private void BtnAchievements_Click(object? sender, EventArgs e)
        {
            // SoundManager.PlayButtonClick();
            var achievementsForm = new AchievementsForm();
            achievementsForm.ShowDialog();
        }

        private void BtnSettings_Click(object? sender, EventArgs e)
        {
            // SoundManager.PlayButtonClick();
            var settingsForm = new SettingsForm();
            settingsForm.ShowDialog();
        }

        private void BtnHelp_Click(object? sender, EventArgs e)
        {
            // SoundManager.PlayButtonClick();
            var helpForm = new HelpForm();
            helpForm.ShowDialog();
        }

        private void BtnExit_Click(object? sender, EventArgs e)
        {
            // SoundManager.PlayButtonClick();
            var result = MessageBox.Show("Are you sure you want to exit?", "Exit Game",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Logger.Log("User exiting application from main menu");
                Application.Exit();
            }
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            base.OnFormClosed(e);
        }
    }
}
