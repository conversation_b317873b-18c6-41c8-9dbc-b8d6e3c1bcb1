using System;
using System.Collections.Generic;

namespace MillionaireGame.Classes
{
    public static class Localization
    {
        private static string _currentLanguage = "en";

        public static string CurrentLanguage
        {
            get
            {
                return _currentLanguage;
            }
            set
            {
                if (value == "en" || value == "ar")
                {
                    _currentLanguage = value;
                    Logger.Log($"Language: {value}");
                }
            }
        }

        public static List<string> AvailableLanguages
        {
            get
            {
                List<string> languages = new List<string>();
                languages.Add("en");
                languages.Add("ar");
                return languages;
            }
        }

        public static string Get(string key, params object[] args)
        {
            string result = GetTranslation(key);
            return args.Length > 0 ? string.Format(result, args) : result;
        }

        private static string GetTranslation(string key)
        {
            if (_currentLanguage == "ar")
            {
                return GetArabicTranslation(key);
            }
            return GetEnglishTranslation(key);
        }

        private static string GetEnglishTranslation(string key)
        {
            if (key == "MainTitle") return "WHO WANTS TO BE A";
            if (key == "MainSubtitle") return "MILLIONAIRE?";
            if (key == "NewGame") return "New Game";
            if (key == "HighScores") return "High Scores";
            if (key == "Statistics") return "Statistics";
            if (key == "Achievements") return "Achievements";
            if (key == "Settings") return "Settings";
            if (key == "Help") return "Help";
            if (key == "Exit") return "Exit";
            if (key == "Level") return "Level";
            if (key == "Question") return "Question";
            if (key == "ConfirmAnswer") return "Confirm Answer";
            if (key == "FiftyFifty") return "50:50";
            if (key == "SwitchQuestion") return "Switch Question";
            if (key == "Withdraw") return "Withdraw";
            if (key == "TimeRemaining") return "Time Remaining";
            if (key == "CorrectAnswer") return "Correct Answer!";
            if (key == "WrongAnswer") return "Wrong Answer!";
            if (key == "GameOver") return "Game Over";
            if (key == "Congratulations") return "Congratulations!";
            if (key == "YouWon") return "You Won";
            if (key == "TotalWinnings") return "Total Winnings";
            if (key == "PlayAgain") return "Play Again";
            if (key == "BackToMenu") return "Back to Menu";
            if (key == "Language") return "Language";
            if (key == "SoundEffects") return "Sound Effects";
            if (key == "Difficulty") return "Difficulty";
            if (key == "Easy") return "Easy";
            if (key == "Medium") return "Medium";
            if (key == "Hard") return "Hard";
            if (key == "SaveSettings") return "Save Settings";
            if (key == "ResetDefaults") return "Reset to Defaults";
            if (key == "HowToPlay") return "How to Play";
            if (key == "GameRules") return "Game Rules";
            if (key == "Lifelines") return "Lifelines";
            if (key == "PrizeLadder") return "Prize Ladder";
            if (key == "Close") return "Close";
            if (key == "PlayerName") return "Player Name";
            if (key == "Score") return "Score";
            if (key == "Date") return "Date";
            if (key == "NoScoresYet") return "No scores yet";
            if (key == "GamesPlayed") return "Games Played";
            if (key == "TotalMoneyWon") return "Total Money Won";
            if (key == "HighestWin") return "Highest Win";
            if (key == "AverageWin") return "Average Win";
            if (key == "WinRate") return "Win Rate";
            if (key == "AchievementName") return "Achievement";
            if (key == "AchievementDescription") return "Description";
            if (key == "AchievementProgress") return "Progress";
            if (key == "Unlocked") return "Unlocked";
            if (key == "Locked") return "Locked";
            if (key == "Points") return "Points";
            if (key == "TotalPoints") return "Total Points";
            if (key == "English") return "English";
            if (key == "Arabic") return "Arabic";
            return key;
        }

        private static string GetArabicTranslation(string key)
        {
            if (key == "MainTitle") return "من يريد أن يصبح";
            if (key == "MainSubtitle") return "مليونيراً؟";
            if (key == "NewGame") return "لعبة جديدة";
            if (key == "HighScores") return "أعلى النتائج";
            if (key == "Statistics") return "الإحصائيات";
            if (key == "Achievements") return "الإنجازات";
            if (key == "Settings") return "الإعدادات";
            if (key == "Help") return "المساعدة";
            if (key == "Exit") return "خروج";
            if (key == "Level") return "المستوى";
            if (key == "Question") return "السؤال";
            if (key == "ConfirmAnswer") return "تأكيد الإجابة";
            if (key == "FiftyFifty") return "50:50";
            if (key == "SwitchQuestion") return "تبديل السؤال";
            if (key == "Withdraw") return "الانسحاب";
            if (key == "TimeRemaining") return "الوقت المتبقي";
            if (key == "CorrectAnswer") return "إجابة صحيحة!";
            if (key == "WrongAnswer") return "إجابة خاطئة!";
            if (key == "GameOver") return "انتهت اللعبة";
            if (key == "Congratulations") return "تهانينا!";
            if (key == "YouWon") return "لقد فزت";
            if (key == "TotalWinnings") return "إجمالي الأرباح";
            if (key == "PlayAgain") return "العب مرة أخرى";
            if (key == "BackToMenu") return "العودة للقائمة";
            if (key == "Language") return "اللغة";
            if (key == "SoundEffects") return "المؤثرات الصوتية";
            if (key == "Difficulty") return "الصعوبة";
            if (key == "Easy") return "سهل";
            if (key == "Medium") return "متوسط";
            if (key == "Hard") return "صعب";
            if (key == "SaveSettings") return "حفظ الإعدادات";
            if (key == "ResetDefaults") return "إعادة تعيين الافتراضيات";
            if (key == "HowToPlay") return "كيفية اللعب";
            if (key == "GameRules") return "قواعد اللعبة";
            if (key == "Lifelines") return "المساعدات";
            if (key == "PrizeLadder") return "سلم الجوائز";
            if (key == "Close") return "إغلاق";
            if (key == "PlayerName") return "اسم اللاعب";
            if (key == "Score") return "النتيجة";
            if (key == "Date") return "التاريخ";
            if (key == "NoScoresYet") return "لا توجد نتائج بعد";
            if (key == "GamesPlayed") return "الألعاب الملعوبة";
            if (key == "TotalMoneyWon") return "إجمالي المال المكتسب";
            if (key == "HighestWin") return "أعلى فوز";
            if (key == "AverageWin") return "متوسط الفوز";
            if (key == "WinRate") return "معدل الفوز";
            if (key == "AchievementName") return "الإنجاز";
            if (key == "AchievementDescription") return "الوصف";
            if (key == "AchievementProgress") return "التقدم";
            if (key == "Unlocked") return "مفتوح";
            if (key == "Locked") return "مقفل";
            if (key == "Points") return "النقاط";
            if (key == "TotalPoints") return "إجمالي النقاط";
            if (key == "English") return "الإنجليزية";
            if (key == "Arabic") return "العربية";
            return key;
        }

        public static void SetLanguage(string languageCode)
        {
            CurrentLanguage = languageCode;
        }

        public static string GetLanguageName(string languageCode)
        {
            if (languageCode == "en") return "English";
            if (languageCode == "ar") return "العربية";
            return languageCode;
        }

        public static bool IsRightToLeft()
        {
            return _currentLanguage == "ar";
        }

        public static void SaveLanguagePreference()
        {
            try
            {
                var settings = GameSettings.Instance;
                settings.CurrentLanguage = _currentLanguage == "ar" ? GameSettings.Language.Arabic : GameSettings.Language.English;
                settings.SaveSettings();
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
            }
        }

        public static void LoadLanguagePreference()
        {
            try
            {
                var settings = GameSettings.Instance;
                _currentLanguage = settings.CurrentLanguage == GameSettings.Language.Arabic ? "ar" : "en";
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                _currentLanguage = "en";
            }
        }
    }
}
