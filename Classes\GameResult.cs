using System;
using System.Collections.Generic;
using System.IO;

namespace MillionaireGame.Classes
{
    /// <summary>
    /// Represents a single game result
    /// </summary>
    public class GameResult
    {
        public string PlayerName { get; set; } = string.Empty;
        public int AmountWon { get; set; }
        public int LevelReached { get; set; }
        public DateTime GameDate { get; set; }
        public TimeSpan GameDuration { get; set; }

        public GameResult()
        {
            GameDate = DateTime.Now;
        }

        public GameResult(string playerName, int amountWon, int levelReached, TimeSpan gameDuration)
        {
            PlayerName = playerName;
            AmountWon = amountWon;
            LevelReached = levelReached;
            GameDate = DateTime.Now;
            GameDuration = gameDuration;
        }
    }

    /// <summary>
    /// Manages saving and loading game results
    /// </summary>
    public static class GameResultsManager
    {
        private static readonly string ResultsFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "results.txt");

        /// <summary>
        /// Save a game result to the results file
        /// Format: Date|PlayerName|AmountWon|LevelReached|GameDurationTicks
        /// </summary>
        public static bool SaveResult(GameResult result)
        {
            try
            {
                Logger.Log($"Saving game result for player: {result.PlayerName}, Amount: {result.AmountWon}");

                // Ensure Data directory exists
                string dataDir = Path.GetDirectoryName(ResultsFilePath);
                if (dataDir != null && !Directory.Exists(dataDir))
                {
                    Directory.CreateDirectory(dataDir);
                }

                // Append new result to file
                using (StreamWriter sw = new StreamWriter(ResultsFilePath, true))
                {
                    string line = $"{result.GameDate:yyyy-MM-dd HH:mm:ss}|{result.PlayerName}|{result.AmountWon}|{result.LevelReached}|{result.GameDuration.Ticks}";
                    sw.WriteLine(line);
                }

                Logger.Log("Game result saved successfully");
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                Logger.Log("Failed to save game result");
                return false;
            }
        }

        /// <summary>
        /// Load all game results from the results file
        /// Format: Date|PlayerName|AmountWon|LevelReached|GameDurationTicks
        /// </summary>
        public static List<GameResult> LoadResults()
        {
            try
            {
                if (!File.Exists(ResultsFilePath))
                {
                    Logger.Log("Results file does not exist, returning empty list");
                    return new List<GameResult>();
                }

                List<GameResult> results = new List<GameResult>();

                using (StreamReader sr = new StreamReader(ResultsFilePath))
                {
                    string line;
                    while ((line = sr.ReadLine()) != null)
                    {
                        // Skip empty lines and comments
                        if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                            continue;

                        string[] parts = line.Split('|');
                        if (parts.Length >= 5)
                        {
                            try
                            {
                                GameResult result = new GameResult();
                                result.GameDate = DateTime.Parse(parts[0]);
                                result.PlayerName = parts[1];
                                result.AmountWon = int.Parse(parts[2]);
                                result.LevelReached = int.Parse(parts[3]);
                                result.GameDuration = TimeSpan.FromTicks(long.Parse(parts[4]));
                                results.Add(result);
                            }
                            catch
                            {
                                // Skip invalid lines
                                continue;
                            }
                        }
                    }
                }

                Logger.Log($"Loaded {results.Count} game results");
                return results;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                Logger.Log("Failed to load game results, returning empty list");
                return new List<GameResult>();
            }
        }

        /// <summary>
        /// Get the top results (highest scores)
        /// </summary>
        public static List<GameResult> GetTopResults(int count = 10)
        {
            try
            {
                var results = LoadResults();

                // Sort using manual comparison (no lambda)
                results.Sort(CompareByAmountWon);

                if (results.Count > count)
                {
                    results.RemoveRange(count, results.Count - count);
                }

                return results;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                return new List<GameResult>();
            }
        }

        // Comparison method for sorting (replaces lambda)
        private static int CompareByAmountWon(GameResult a, GameResult b)
        {
            return b.AmountWon.CompareTo(a.AmountWon);
        }

        /// <summary>
        /// Get results for a specific player
        /// </summary>
        public static List<GameResult> GetPlayerResults(string playerName)
        {
            try
            {
                var results = LoadResults();
                var playerResults = new List<GameResult>();

                foreach (var result in results)
                {
                    if (string.Equals(result.PlayerName, playerName, StringComparison.OrdinalIgnoreCase))
                    {
                        playerResults.Add(result);
                    }
                }

                // Sort using the same comparison method
                playerResults.Sort(CompareByAmountWon);
                return playerResults;
            }
            catch (Exception ex)
            {
                Logger.LogException(ex);
                return new List<GameResult>();
            }
        }
    }
}
