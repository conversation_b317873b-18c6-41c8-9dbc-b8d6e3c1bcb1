using System;
using System.Windows.Forms;
using System.Drawing;
using MillionaireGame.Classes;

namespace MillionaireGame
{
    public class ResultForm : Form
    {
        private Label? lblMessage;
        private Label? lblAmount;
        private Label? lblPlayerName;
        private TextBox? txtPlayerName;
        private Button? btnSaveResult;
        private Button? btnExit;
        private Button? btnPlayAgain;
        private Button? btnViewHighScores;
        private Panel? topPanel;
        private Panel? inputPanel;

        private readonly int _amountWon;
        private readonly int _levelReached;
        private readonly TimeSpan _gameDuration;

        public ResultForm(string amount, int levelReached, TimeSpan gameDuration)
        {
            if (!int.TryParse(amount, out _amountWon))
                _amountWon = 0;

            _levelReached = levelReached;
            _gameDuration = gameDuration;

            InitializeComponents();
            SetupResultDisplay();
        }

        public ResultForm(string amount) : this(amount, 1, TimeSpan.Zero)
        {
        }

        private void InitializeComponents()
        {
            this.Text = "Game Result";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(24, 33, 58);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;

            topPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 100,
                BackColor = Color.FromArgb(34, 47, 86)
            };

            lblMessage = new Label
            {
                Text = "Game Over",
                AutoSize = false,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Arial", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 215, 0),
                Dock = DockStyle.Fill
            };

            lblAmount = new Label
            {
                Location = new Point(50, 120),
                Size = new Size(500, 50),
                Font = new Font("Arial", 24, FontStyle.Bold),
                ForeColor = Color.White,
                TextAlign = ContentAlignment.MiddleCenter,
                Text = "0"
            };

            inputPanel = new Panel
            {
                Location = new Point(50, 180),
                Size = new Size(500, 80),
                BackColor = Color.FromArgb(34, 47, 86),
                BorderStyle = BorderStyle.FixedSingle
            };

            lblPlayerName = new Label
            {
                Location = new Point(20, 15),
                Size = new Size(150, 25),
                Text = "Enter your name:",
                Font = new Font("Arial", 12, FontStyle.Bold),
                ForeColor = Color.White
            };

            txtPlayerName = new TextBox
            {
                Location = new Point(20, 45),
                Size = new Size(200, 25),
                Font = new Font("Arial", 12),
                MaxLength = 50
            };

            btnSaveResult = new Button
            {
                Location = new Point(240, 40),
                Size = new Size(120, 35),
                Text = "Save Result",
                Font = new Font("Arial", 10, FontStyle.Bold),
                BackColor = Color.FromArgb(0, 120, 215),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnSaveResult.Click += BtnSaveResult_Click;

            btnViewHighScores = new Button
            {
                Location = new Point(50, 280),
                Size = new Size(140, 40),
                Text = "High Scores",
                Font = new Font("Arial", 12),
                BackColor = Color.FromArgb(0, 150, 136),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnViewHighScores.Click += BtnViewHighScores_Click;

            btnPlayAgain = new Button
            {
                Location = new Point(210, 280),
                Size = new Size(120, 40),
                Text = "Play Again",
                Font = new Font("Arial", 12),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnPlayAgain.Click += BtnPlayAgain_Click;

            btnExit = new Button
            {
                Location = new Point(350, 280),
                Size = new Size(100, 40),
                Text = "Exit",
                Font = new Font("Arial", 12),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };
            btnExit.Click += BtnExit_Click;

            inputPanel.Controls.Add(lblPlayerName);
            inputPanel.Controls.Add(txtPlayerName);
            inputPanel.Controls.Add(btnSaveResult);

            topPanel.Controls.Add(lblMessage);

            this.Controls.Add(topPanel);
            this.Controls.Add(lblAmount);
            this.Controls.Add(inputPanel);
            this.Controls.Add(btnViewHighScores);
            this.Controls.Add(btnPlayAgain);
            this.Controls.Add(btnExit);
        }

        private void SetupResultDisplay()
        {
            if (lblAmount != null) lblAmount.Text = _amountWon.ToString("N0");

            if (_amountWon >= 1000000)
            {
                if (lblMessage != null) lblMessage.Text = "🎉 CONGRATULATIONS! YOU WON THE MILLION! 🎉";
            }
            else if (_amountWon > 0)
            {
                if (lblMessage != null) lblMessage.Text = $"Game Over - Level {_levelReached} Reached!";
            }
            else
            {
                if (lblMessage != null) lblMessage.Text = "Game Over - Better luck next time!";
            }

            if (txtPlayerName != null)
            {
                txtPlayerName.Text = $"Player_{DateTime.Now:MMddHHmm}";
                txtPlayerName.SelectAll();
                txtPlayerName.Focus();
            }
        }

        private void BtnSaveResult_Click(object? sender, EventArgs e)
        {
            if (txtPlayerName == null) return;
            string playerName = txtPlayerName.Text.Trim();
            if (string.IsNullOrEmpty(playerName))
            {
                MessageBox.Show("Please enter your name to save the result.", "Name Required",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtPlayerName.Focus();
                return;
            }

            var result = new GameResult(playerName, _amountWon, _levelReached, _gameDuration);
            bool saved = GameResultsManager.SaveResult(result);

            if (saved)
            {
                var allResults = GameResultsManager.LoadResults();
                AchievementManager.CheckAchievements(allResults);

                MessageBox.Show($"Result saved successfully!\n\nPlayer: {playerName}\nAmount: {_amountWon:N0}\nLevel: {_levelReached}\nTime: {_gameDuration:mm\\:ss}",
                    "Result Saved", MessageBoxButtons.OK, MessageBoxIcon.Information);
                if (btnSaveResult != null)
                {
                    btnSaveResult.Enabled = false;
                    btnSaveResult.Text = "Saved ✓";
                }
            }
            else
            {
                MessageBox.Show("Failed to save result. Please try again.", "Save Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnViewHighScores_Click(object? sender, EventArgs e)
        {
            var highScoresForm = new Forms.HighScoresForm();
            highScoresForm.ShowDialog();
        }

        private void BtnPlayAgain_Click(object? sender, EventArgs e)
        {
            var gm = new MillionaireGame.Classes.MainGameControl();
            var startForm = new StartForm(gm);
            startForm.Show();
            this.Close();
        }

        private void BtnExit_Click(object? sender, EventArgs e)
        {
            Application.Exit();
        }
    }
}

