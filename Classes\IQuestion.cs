using System;

namespace MillionaireGame.Classes
{
    // Interface for questions - demonstrates the concept of interfaces
    // An interface is like a contract that classes must follow
    // It defines WHAT methods a class must have, but not HOW they work
    // This is useful when you want different types of questions that all share common behavior
    public interface IQuestion
    {
        // Properties that all questions must have
        int Level { get; }
        string Text { get; }
        
        // Method that all questions must implement
        // This checks if a given answer is correct
        bool CheckAnswer(string userAnswer);
    }
}

