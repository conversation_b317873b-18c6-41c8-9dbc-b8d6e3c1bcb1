[{"Level": 1, "Text": "What is the capital of France?", "CorrectAnswer": "Paris", "WrongAnswers": ["London", "Berlin", "Madrid", "Rome", "Athens", "Lisbon"]}, {"Level": 1, "Text": "Which color is the sky on a clear day?", "CorrectAnswer": "Blue", "WrongAnswers": ["Red", "Yellow", "Green", "Black", "Orange", "Pink"]}, {"Level": 1, "Text": "How many days are there in a week?", "CorrectAnswer": "7", "WrongAnswers": ["5", "6", "8", "9", "10", "12"]}, {"Level": 1, "Text": "Which animal says \"meow\"?", "CorrectAnswer": "Cat", "WrongAnswers": ["Dog", "Horse", "Sheep", "Cow", "Goa<PERSON>", "Pig"]}, {"Level": 1, "Text": "What is 2 + 2?", "CorrectAnswer": "4", "WrongAnswers": ["3", "5", "6", "7", "8", "9"]}, {"Level": 2, "Text": "Which planet is known as the Red Planet?", "CorrectAnswer": "Mars", "WrongAnswers": ["Venus", "Earth", "Jupiter", "Saturn", "Neptune", "Mercury"]}, {"Level": 2, "Text": "What is H2O commonly known as?", "CorrectAnswer": "Water", "WrongAnswers": ["Salt", "Oxygen", "Hydrogen", "Fire", "Sugar", "Air"]}, {"Level": 2, "Text": "Which shape has three sides?", "CorrectAnswer": "Triangle", "WrongAnswers": ["Square", "Circle", "Rectangle", "Pentagon", "Hexagon", "Octagon"]}, {"Level": 2, "Text": "What is the opposite of hot?", "CorrectAnswer": "Cold", "WrongAnswers": ["Warm", "Heat", "Boiling", "Burning", "Dry", "Wet"]}, {"Level": 2, "Text": "Which season comes after spring?", "CorrectAnswer": "Summer", "WrongAnswers": ["Winter", "Autumn", "Rainy", "Dry", "Foggy", "Stormy"]}, {"Level": 3, "Text": "Who wrote \"Romeo and Juliet\"?", "CorrectAnswer": "Shakespeare", "WrongAnswers": ["<PERSON>", "<PERSON>", "To<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"]}, {"Level": 3, "Text": "What is 5 x 5?", "CorrectAnswer": "25", "WrongAnswers": ["20", "15", "30", "10", "50", "40"]}, {"Level": 3, "Text": "Which ocean is the largest?", "CorrectAnswer": "Pacific", "WrongAnswers": ["Atlantic", "Indian", "Arctic", "Southern", "Red", "Black"]}, {"Level": 3, "Text": "What do bees make?", "CorrectAnswer": "Honey", "WrongAnswers": ["Milk", "Wax", "Jam", "Cheese", "Juice", "Oil"]}, {"Level": 3, "Text": "How many continents are there?", "CorrectAnswer": "7", "WrongAnswers": ["5", "6", "8", "9", "4", "3"]}, {"Level": 4, "Text": "What gas do humans breathe in?", "CorrectAnswer": "Oxygen", "WrongAnswers": ["Carbon", "Hydrogen", "Nitrogen", "Helium", "Smoke", "Steam"]}, {"Level": 4, "Text": "Which month has 28 days?", "CorrectAnswer": "All", "WrongAnswers": ["February", "January", "June", "March", "May", "October"]}, {"Level": 4, "Text": "How many hours are in a day?", "CorrectAnswer": "24", "WrongAnswers": ["12", "25", "26", "30", "20", "10"]}, {"Level": 4, "Text": "What do cows drink?", "CorrectAnswer": "Water", "WrongAnswers": ["Milk", "Juice", "Tea", "Coffee", "Oil", "Beer"]}, {"Level": 4, "Text": "What is the capital of Italy?", "CorrectAnswer": "Rome", "WrongAnswers": ["Milan", "Venice", "Florence", "Naples", "Pisa", "Turin"]}, {"Level": 5, "Text": "What is the boiling point of water?", "CorrectAnswer": "100", "WrongAnswers": ["90", "80", "70", "60", "110", "120"]}, {"Level": 5, "Text": "Which instrument has black and white keys?", "CorrectAnswer": "Piano", "WrongAnswers": ["Guitar", "Drum", "Violin", "Flute", "Harp", "Saxophone"]}, {"Level": 5, "Text": "Which bird is a symbol of peace?", "CorrectAnswer": "Dove", "WrongAnswers": ["Eagle", "Crow", "Owl", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"]}, {"Level": 5, "Text": "How many legs does a spider have?", "CorrectAnswer": "8", "WrongAnswers": ["6", "4", "10", "12", "14", "2"]}, {"Level": 5, "Text": "What is the capital of Spain?", "CorrectAnswer": "Madrid", "WrongAnswers": ["Barcelona", "Valencia", "Seville", "Bilbao", "Granada", "Toledo"]}, {"Level": 6, "Text": "Which metal is liquid at room temperature?", "CorrectAnswer": "Mercury", "WrongAnswers": ["Iron", "Gold", "Silver", "Copper", "Zinc", "Lead"]}, {"Level": 6, "Text": "What is the square root of 81?", "CorrectAnswer": "9", "WrongAnswers": ["8", "7", "6", "10", "11", "12"]}, {"Level": 6, "Text": "Which planet is closest to the Sun?", "CorrectAnswer": "Mercury", "WrongAnswers": ["Venus", "Earth", "Mars", "Jupiter", "Saturn", "Neptune"]}, {"Level": 6, "Text": "What is the main ingredient in bread?", "CorrectAnswer": "Flour", "WrongAnswers": ["Rice", "Milk", "Meat", "Cheese", "Potato", "Corn"]}, {"Level": 6, "Text": "Who painted the Mona Lisa?", "CorrectAnswer": "Da Vinci", "WrongAnswers": ["Picasso", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Monet"]}, {"Level": 7, "Text": "Which blood type is the universal donor?", "CorrectAnswer": "O Negative", "WrongAnswers": ["O Positive", "A Positive", "A Negative", "B Positive", "B Negative", "AB Positive"]}, {"Level": 7, "Text": "What is the capital of Germany?", "CorrectAnswer": "Berlin", "WrongAnswers": ["Munich", "Frankfurt", "Hamburg", "Cologne", "Dresden", "Stuttgart"]}, {"Level": 7, "Text": "How many players are on a soccer team?", "CorrectAnswer": "11", "WrongAnswers": ["9", "10", "12", "8", "13", "7"]}, {"Level": 7, "Text": "What is the fastest land animal?", "CorrectAnswer": "Cheetah", "WrongAnswers": ["Horse", "Lion", "Tiger", "<PERSON><PERSON>", "<PERSON>", "Gazelle"]}, {"Level": 7, "Text": "Which organ pumps blood in the body?", "CorrectAnswer": "Heart", "WrongAnswers": ["<PERSON><PERSON>", "Brain", "Liver", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"Level": 8, "Text": "What is the currency of the USA?", "CorrectAnswer": "Dollar", "WrongAnswers": ["Euro", "Pound", "Yen", "Franc", "Peso", "Rupee"]}, {"Level": 8, "Text": "Who discovered gravity?", "CorrectAnswer": "<PERSON>", "WrongAnswers": ["<PERSON>", "Tesla", "Galileo", "Edison", "<PERSON>", "Hawking"]}, {"Level": 8, "Text": "What is the capital of Japan?", "CorrectAnswer": "Tokyo", "WrongAnswers": ["Osaka", "Kyoto", "Nagoya", "Hiroshima", "Sapporo", "Nagasaki"]}, {"Level": 8, "Text": "How many teeth does an adult human have?", "CorrectAnswer": "32", "WrongAnswers": ["30", "28", "26", "34", "36", "40"]}, {"Level": 8, "Text": "Which gas do plants release?", "CorrectAnswer": "Oxygen", "WrongAnswers": ["Carbon dioxide", "Hydrogen", "Nitrogen", "Helium", "Methane", "Steam"]}, {"Level": 9, "Text": "What is the largest desert in the world?", "CorrectAnswer": "Sahara", "WrongAnswers": ["Gobi", "Kalahari", "Arabian", "<PERSON><PERSON>", "Patagonia", "Mojave"]}, {"Level": 9, "Text": "Which language has the most native speakers?", "CorrectAnswer": "Mandarin", "WrongAnswers": ["English", "Spanish", "Hindi", "Arabic", "French", "Portuguese"]}, {"Level": 9, "Text": "What is the hardest natural substance?", "CorrectAnswer": "Diamond", "WrongAnswers": ["Gold", "Iron", "Steel", "Silver", "Quartz", "Platinum"]}, {"Level": 9, "Text": "Which organ is used for breathing?", "CorrectAnswer": "<PERSON><PERSON><PERSON>", "WrongAnswers": ["Heart", "Kidneys", "Liver", "<PERSON><PERSON><PERSON>", "Brain", "Intestine"]}, {"Level": 9, "Text": "How many planets are in the solar system?", "CorrectAnswer": "8", "WrongAnswers": ["7", "9", "6", "10", "11", "5"]}, {"Level": 10, "Text": "What is the capital of Canada?", "CorrectAnswer": "Ottawa", "WrongAnswers": ["Toronto", "Vancouver", "Montreal", "Calgary", "Quebec", "Edmonton"]}, {"Level": 10, "Text": "Who was the first man on the moon?", "CorrectAnswer": "<PERSON>", "WrongAnswers": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]}, {"Level": 10, "Text": "What is the speed of light?", "CorrectAnswer": "299792458", "WrongAnswers": ["300000", "150000", "100000", "200000", "250000", "400000"]}, {"Level": 10, "Text": "Which gas makes balloons float?", "CorrectAnswer": "Helium", "WrongAnswers": ["Oxygen", "Hydrogen", "Nitrogen", "Steam", "Air", "Carbon dioxide"]}, {"Level": 10, "Text": "Which is the largest mammal?", "CorrectAnswer": "Blue Whale", "WrongAnswers": ["Elephant", "Shark", "Giraffe", "<PERSON><PERSON>", "Rhino", "Whale Shark"]}, {"Level": 11, "Text": "What is the smallest prime number?", "CorrectAnswer": "2", "WrongAnswers": ["1", "3", "5", "7", "9", "11"]}, {"Level": 11, "Text": "Which planet has rings?", "CorrectAnswer": "Saturn", "WrongAnswers": ["Jupiter", "Uranus", "Neptune", "Mars", "Earth", "Venus"]}, {"Level": 11, "Text": "Who invented the telephone?", "CorrectAnswer": "Bell", "WrongAnswers": ["Edison", "Tesla", "<PERSON><PERSON>", "<PERSON>", "Faraday", "Volta"]}, {"Level": 11, "Text": "What is the capital of Australia?", "CorrectAnswer": "Canberra", "WrongAnswers": ["Sydney", "Melbourne", "Perth", "Adelaide", "Brisbane", "Hobart"]}, {"Level": 11, "Text": "Which continent is Egypt in?", "CorrectAnswer": "Africa", "WrongAnswers": ["Asia", "Europe", "South America", "North America", "Australia", "Antarctica"]}, {"Level": 12, "Text": "What is the chemical symbol for gold?", "CorrectAnswer": "Au", "WrongAnswers": ["Ag", "Fe", "<PERSON><PERSON>", "Pb", "Zn", "Sn"]}, {"Level": 12, "Text": "Which organ is called the \"control center\"?", "CorrectAnswer": "Brain", "WrongAnswers": ["Heart", "Liver", "Kidneys", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Skin"]}, {"Level": 12, "Text": "What is the largest country in the world?", "CorrectAnswer": "Russia", "WrongAnswers": ["China", "USA", "Canada", "Brazil", "India", "Australia"]}, {"Level": 12, "Text": "What is the capital of Brazil?", "CorrectAnswer": "Brasilia", "WrongAnswers": ["Rio de Janeiro", "Sao Paulo", "Salvador", "Recife", "Fortaleza", "Manaus"]}, {"Level": 12, "Text": "What is frozen water called?", "CorrectAnswer": "Ice", "WrongAnswers": ["Snow", "<PERSON>l", "Steam", "Fog", "Rain", "<PERSON>"]}, {"Level": 13, "Text": "Which element do humans need to breathe?", "CorrectAnswer": "Oxygen", "WrongAnswers": ["Carbon", "Nitrogen", "Hydrogen", "Helium", "Sulfur", "Iron"]}, {"Level": 13, "Text": "Who painted \"Starry Night\"?", "CorrectAnswer": "<PERSON>", "WrongAnswers": ["Da Vinci", "Picasso", "Monet", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"Level": 13, "Text": "What is the capital of Egypt?", "CorrectAnswer": "Cairo", "WrongAnswers": ["Alexandria", "Giza", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Port Said", "Mansoura"]}, {"Level": 13, "Text": "What is 12 x 12?", "CorrectAnswer": "144", "WrongAnswers": ["124", "132", "100", "121", "156", "200"]}, {"Level": 13, "Text": "Which instrument has strings?", "CorrectAnswer": "Guitar", "WrongAnswers": ["Drum", "Trumpet", "Flute", "Saxophone", "Trom<PERSON>", "Violin"]}, {"Level": 14, "Text": "Which planet is called the \"giant planet\"?", "CorrectAnswer": "Jupiter", "WrongAnswers": ["Saturn", "Neptune", "Uranus", "Earth", "Mars", "Venus"]}, {"Level": 14, "Text": "What is the national sport of Japan?", "CorrectAnswer": "Sumo", "WrongAnswers": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Baseball", "<PERSON><PERSON>", "Soccer", "Tennis"]}, {"Level": 14, "Text": "What is the capital of Russia?", "CorrectAnswer": "Moscow", "WrongAnswers": ["Saint Petersburg", "Kazan", "Sochi", "Novosibirsk", "Omsk", "Vladivostok"]}, {"Level": 14, "Text": "Which organ filters blood?", "CorrectAnswer": "Kidneys", "WrongAnswers": ["Heart", "Liver", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Spleen", "<PERSON><PERSON><PERSON>"]}, {"Level": 14, "Text": "What is the longest river in the world?", "CorrectAnswer": "Nile", "WrongAnswers": ["Amazon", "<PERSON><PERSON><PERSON>", "Mississippi", "Danube", "Ganges", "Volga"]}, {"Level": 15, "Text": "What is the chemical formula of table salt?", "CorrectAnswer": "NaCl", "WrongAnswers": ["<PERSON><PERSON>", "H2O", "CO2", "O2", "CH4", "CaCO3"]}, {"Level": 15, "Text": "Which is the rarest blood type?", "CorrectAnswer": "AB Negative", "WrongAnswers": ["O Positive", "A Positive", "B Positive", "O Negative", "A Negative", "B Negative"]}, {"Level": 15, "Text": "What is the capital of China?", "CorrectAnswer": "Beijing", "WrongAnswers": ["Shanghai", "Hong Kong", "Shenzhen", "Guangzhou", "Nanjing", "Chengdu"]}, {"Level": 15, "Text": "Who developed the theory of relativity?", "CorrectAnswer": "<PERSON>", "WrongAnswers": ["<PERSON>", "Tesla", "Hawking", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"]}, {"Level": 15, "Text": "Which is the tallest mountain in the world?", "CorrectAnswer": "Everest", "WrongAnswers": ["K2", "Kangchenjunga", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Nanga Parbat"]}]